// Fixed fileValidationMiddlewares.ts

import { Injectable, NestMiddleware, BadRequestException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class FileValidationMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const { files } = req;

    console.log("files", files);

    // Make file validation optional - allow updates without files
    if (!files) {
      return next();
    }

    const validMimeTypes = ['image/jpeg', 'image/png', 'image/jpg'];
    const maxFileSize = 5 * 1024 * 1024; // 5MB

    // Validate profilePicUrl if present
    if (files['profilePicUrl']) {
      const profilePic = files['profilePicUrl'][0];
      if (!validMimeTypes.includes(profilePic.mimetype)) {
        throw new BadRequestException('Profile picture must be a JPEG or PNG.');
      }
      if (profilePic.size > maxFileSize) {
        throw new BadRequestException('Profile picture size must be less than 5MB.');
      }
    }

    // Validate liscenceImages if present
    if (files['liscenceImages']) {
      for (const licenseImage of files['liscenceImages']) {
        if (!validMimeTypes.includes(licenseImage.mimetype)) {
          throw new BadRequestException('License images must be JPEG or PNG.');
        }
        if (licenseImage.size > maxFileSize) {
          throw new BadRequestException('License images must be less than 5MB.');
        }
      }
    }

    next();
  }
}