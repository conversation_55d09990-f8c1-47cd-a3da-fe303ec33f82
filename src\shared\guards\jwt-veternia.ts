// src/shared/guards/jwt-veternia.guard.ts
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtAuthGuard } from './jwt-auth.guard';
import { ConfigService } from '@nestjs/config';
import { UsersService } from 'src/modules/users/users.service';

@Injectable()
export class JwtVeterniaGuard extends JwtAuthGuard implements CanActivate {
  constructor(configService: ConfigService) {
    super(configService);
  }

  canActivate(context: ExecutionContext): boolean {
    // First verify the JWT token using parent guard
    const isValidToken = super.canActivate(context);
    
    if (!isValidToken) {
      return false;
    }

    // Get the request object
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // Check if user has veterinarian role
    if (!user || user.userType !== 'veternia') {
      throw new ForbiddenException('Access denied. Veterinarian role required.');
    }

    return true;
  }
}