import { ConflictException,Injectable,BadRequestException } from '@nestjs/common';
import { UserSchema } from './schemas/user.schema';
import { InjectModel } from '@nestjs/mongoose';
import { User,UserDocument } from './schemas/user.schema';
import { Model ,Types} from 'mongoose';
import { SignupDto } from '../auth/dto/signup.dto';
import { Otp,OtpDocument } from './schemas/otp.schema';
import { updateUserDto } from './dto/updateUser.dto';
import { updateVeterniaDto } from './dto/updateVeternia.dto';
import { UploadService } from '../upload/upload.service';
import {CompleteVeterniaProfileDto} from "./dto/completeVeternia.dto"
import { booking, bookingDocument } from '../booking/schemas/bookingSchemas';
import { getAvailableSlotDto } from './dto/getAvailableSlot.dto';
import { generateOtp } from 'src/shared/utils/generateOtp';
//import { AuthModule } from '../auth/auth.module';
import { AuthService } from '../auth/auth.service';
//import { PaymentService } from '../../payment/payment.service';
import { Type } from '@aws-sdk/client-s3';



@Injectable()
export class UsersService {
    constructor(
        @InjectModel(User.name) private userModel: Model<UserDocument>,
        @InjectModel(Otp.name) private otpModel: Model<OtpDocument>,
        @InjectModel(booking.name) private bookingModel: Model<bookingDocument>,
       // @InjectModel(AuthModule.name) private authModule: AuthModule,
        private readonly uploadService:UploadService,
        //private readonly authService:AuthService,
       // private readonly paymentService:PaymentService
      ) {}
      async createOtp(userId: string, otp: string, expiresAt: Date) {
        return this.otpModel.create({ userId, otp, expireAt:expiresAt });
      }
      async create(dto: SignupDto): Promise<UserDocument> {
        const user = new this.userModel(dto);
        await user.save();
        //const userWithConnectAccount=this.paymentService.createStripeConnect(user._id as string)
        return user;
      }

      async findByEmail(email:string):Promise<UserDocument>{
        return this.userModel.findOne({email:email}).exec()
      }
      async findById(id:string):Promise<User>{
        return this.userModel.findById(id).exec()
      }
      async updatePassword(id:string,hashedPassword:string):Promise<User>{
        return this.userModel.findByIdAndUpdate(id,{password:hashedPassword},{new:true})
      }
      async existsByEmail(email:string):Promise<boolean>{
        return !!(await this.userModel.exists({email}))
      }
      async deleteOtps(userId: string) {
        await this.otpModel.deleteMany({ userId });
      }
      
      async verifyOtp(userId: string, otp: string): Promise<boolean> {
        console.log("userId",userId);
        console.log("otp",otp);
        //const findAll=await this.otpModel.find();
        //console.log("findAll",findAll)
        const findOtp = await this.otpModel.findOne({
          userId: userId,
          otp:otp.toString(),
         expireAt: { $gt: new Date() },
        });
      console.log("Find Otp",findOtp)
        if (!findOtp) {
          return false;
        }

      console.log("findOtp.expireAt",findOtp.expireAt);
      // comapre date after 1 min 
      const currentTime = new Date();
      console.log("currentTime",currentTime);
      const otpExpireTime = findOtp.expireAt;
      if (otpExpireTime < currentTime) {
        return false;
      }
      
        
     
       const result= await this.userModel.findByIdAndUpdate(
            userId,
            { isVerified: true },
            { new: true }
          );
          console.log("result",result)
          await this.otpModel.deleteMany({ userId });
        return true;
      }

      async verifyForgotOtp(userId: string, otp: string): Promise<boolean> {
        console.log("userId",userId);
        console.log("otp",otp);
        const findOtp = await this.otpModel.findOne({
          userId: userId,
          otp:otp,
         // expireAt: { $gt: new Date() },
        });
      
        if (!findOtp) {
          return false;
        }
    
        return true;
      }
      async updateProfile(userId: string, dto: updateUserDto, file?: Express.Multer.File) {
        try {
          if (file) {
            console.log("file")
            
            const fileUrl = await this.uploadService.uploadFiles(file);
            dto.profilePicUrl = fileUrl;
            console.log("profile url",fileUrl)
          }
            //console.log("userId",userId)
      
          const updatedUser = await this.userModel.findByIdAndUpdate(
            userId,
            { $set: dto },
            { new: true }
          );
          console.log("Update user",updatedUser);
          return { message: 'Profile updated successfully',
             user: updatedUser,
             };
        } catch (error) {
         
          console.error(error);
          throw new ConflictException('Failed to update profile');
        }
      }

      async updateVeterniaProfile(userId: string, dto: updateVeterniaDto, files?: { [fieldname: string]: Express.Multer.File[] }, licenceImagesToDelete?: string[]) {
  try {
    const uploadedFileUrls = {};

    const user = await this.userModel.findById(userId);
    console.log("user",user);

    if(!user){
      throw new BadRequestException('User not found');
    }
    if(user.userType!=="veternia"){
      throw new BadRequestException('User is not a veternia');
    }



    if (files && files['profilePicUrl'] && files['profilePicUrl'].length > 0) {

      const profilePicUrl = await this.uploadService.uploadFiles(files['profilePicUrl'][0]);
      user.profilePicUrl = profilePicUrl;
      //uploadedFileUrls['profilePicUrl'] = profilePicUrl;
      //console.log("profilePicUrl", profilePicUrl);
    }

    
    if (files && files['liscenceImages'] && files['liscenceImages'].length > 0) {
      //const licenseImageUrls = [];
      for (const licenseImage of files['liscenceImages']) {
        const licenseImageUrl = await this.uploadService.uploadFiles(licenseImage);
        user.liscenceImages.push(licenseImageUrl);
      }
      //uploadedFileUrls['liscenceImages'] = licenseImageUrls;
      //console.log("licenseImageUrls", licenseImageUrls);
    }

    
    //find user by id
    //const user = await this.userModel.findById(userId);
    console.log("type of licenseIagesto delete",typeof licenceImagesToDelete);

    const imagesToDelete = licenceImagesToDelete as | string[] | undefined;
    console.log(licenceImagesToDelete);
    
    if (imagesToDelete && imagesToDelete.length > 0) {
      console.log("imagesToDelete", imagesToDelete);    
      if(imagesToDelete && imagesToDelete.length>0){
        for (const imageUrl of imagesToDelete) {
          const res = await this.uploadService.deleteFile(imageUrl);
          console.log(res)
          if(res.$metadata.httpStatusCode === 204){
           // i want to pop the image from the array
           user.liscenceImages=user.liscenceImages.filter((image:string) => image !== imageUrl);
          }
        }
      }
    }
    await user.save();
    

    let parsedAvailability = dto.availability;
    if (typeof dto.availability === 'string') {
      try {
        parsedAvailability = JSON.parse(dto.availability);
        console.log("availability parsed", parsedAvailability);
      } catch (error) {
        console.error('Error parsing availability JSON:', error);
        // Use default availability structure if parsing fails
        parsedAvailability = {
          monday: { available: false, start: '', end: '' },
          tuesday: { available: false, start: '', end: '' },
          wednesday: { available: false, start: '', end: '' },
          thursday: { available: false, start: '', end: '' },
          friday: { available: false, start: '', end: '' },
          saturday: { available: false, start: '', end: '' },
          sunday: { available: false, start: '', end: '' },
        };
      }
    }
    
    const updateData = { 
      ...dto, 
      ...uploadedFileUrls,
      availability: parsedAvailability
    };

    
    const updatedUser = await this.userModel.findByIdAndUpdate(
      userId,
      { 
        $set: updateData
      },
      { 
        new: true,
        runValidators: true
      }
    );

    if (!updatedUser) {
      throw new ConflictException('User not found');
    }

    return {
      message: 'Profile updated successfully',
      user: updatedUser,
    };
  } catch (error) {
    console.error('Error in updateVeterniaProfile:', error);
    throw new ConflictException('Failed to update profile');
  }
}



async getAllVeternia(page: number = 1, limit: number = 10) {
  try {
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.userModel
        .find({ userType: "veternia" })
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }), // Changed from 1 to -1 for descending order
      this.userModel.countDocuments({ userType: "veternia" })
    ]);

    return {
      success: true,
      currentPage: page,
      totalPages: Math.ceil(total / limit),
      totalItems: total,
      items: data
    };
  } catch (error) {
    console.error("Error fetching veternia users:", error);
    return {
      success: false,
      message: "Failed to fetch veternia users"
    };
  }
}
      
      async getAllAvailableSlots(dto: getAvailableSlotDto) {
        try {
          const vet = await this.userModel.findById(dto.veterniaId).lean();
          if (!vet) {
            throw new Error('Veternia not found');
          }
      
          const date = dto.date || new Date().toISOString().split('T')[0];
          const dayOfWeek = new Date(date)
            .toLocaleDateString('en-US', { weekday: 'long' }) // changed locale to en-US for correct day names
            .toLowerCase();
      
          const dayAvailability = vet.availability?.[dayOfWeek];
          if (!dayAvailability || !dayAvailability.available) {
            return { availableSlots: [] };
          }
      
          const bookings = await this.bookingModel.find({
            veterniaId: dto.veterniaId,
            startTime: {
              $gte: new Date(`${date}T00:00:00.000Z`),
              $lt: new Date(`${date}T23:59:59.999Z`),
            },
          }).lean();
      
          // Duration in minutes (could be made dynamic)
          const slotDuration = 15;
      
          // Generate all slots between start and end time
          const startTime = new Date(`${date}T${dayAvailability.start}:00Z`);
          const endTime = new Date(`${date}T${dayAvailability.end}:00Z`);
          const allSlots: { start: Date; end: Date }[] = [];
      
          let current = new Date(startTime);
          while (current < endTime) {
            const next = new Date(current.getTime() + slotDuration * 60000);
            allSlots.push({ start: new Date(current), end: next });
            current = next;
          }
      
          // Filter out slots that overlap with bookings
          const bookedRanges = bookings.map(b => ({
            start: new Date(b.startTime),
            end: new Date(b.endTime),
          }));
      
          const availableSlots = allSlots.filter(slot =>
            !bookedRanges.some(
              booked =>
                (slot.start >= booked.start && slot.start < booked.end) ||
                (slot.end > booked.start && slot.end <= booked.end) ||
                (booked.start >= slot.start && booked.start < slot.end)
            )
          );
      
          // Convert available slots to time strings
          const formattedSlots = availableSlots.map(s => ({
            start: s.start.toISOString(),
            end: s.end.toISOString(),
          }));
      
          return {
            date,
            day: dayOfWeek,
            availableSlots: formattedSlots,
          };
        } catch (error) {
          console.error(error);
          throw new Error('Error fetching available slots');
        }
      }

async isSlotAvailable(
    veterniaId: string,
    date: string,
    startTime: string,
    endTime: string,
): Promise<{ available: boolean; message: string }> {
    const vet = await this.userModel.findById(veterniaId).lean();
    if (!vet) {
        return { available: false, message: 'Veternia not found' };
    }

    const dayOfWeek = new Date(date)
        .toLocaleDateString('en-US', { weekday: 'long' })
        .toLowerCase();

    const dayAvailability = vet.availability?.[dayOfWeek];
    if (!dayAvailability || !dayAvailability.available) {
        return { available: false, message: 'Vet not available on this day' };
    }

    // FIX: Use UTC timezone consistently with createBooking
    const slotStart = new Date(`${date}T${startTime}:00Z`);
    const slotEnd = new Date(`${date}T${endTime}:00Z`);

    if (slotEnd <= slotStart) {
        return { available: false, message: 'End time must be after start time' };
    }

    // FIX: Also use UTC for working hours comparison
    const workingStart = new Date(`${date}T${dayAvailability.start}:00Z`);
    const workingEnd = new Date(`${date}T${dayAvailability.end}:00Z`);

    if (slotStart < workingStart || slotEnd > workingEnd) {
        return { available: false, message: 'Requested slot is outside working hours' };
    }

    // FIX: Query should look for exact datetime matches in the database
    const overlappingBookings = await this.bookingModel.find({
        veterniaId,
        $or: [
            {
                // Check for any overlap between existing bookings and requested slot
                startTime: { $lt: slotEnd },
                endTime: { $gt: slotStart },
            },
        ],
    }).lean();

    if (overlappingBookings.length > 0) {
        return { available: false, message: 'Requested slot is already booked' };
    }

    return { available: true, message: 'Slot is available' };
}


      async completeVeterniaProfile(userId: string, dto: updateVeterniaDto, files?: { [fieldname: string]: Express.Multer.File[] }) {
  try {
    const uploadedFileUrls = {};

    if (files && files['profilePicUrl'] && files['profilePicUrl'].length > 0) {
      const profilePicUrl = await this.uploadService.uploadFiles(files['profilePicUrl'][0]);
      uploadedFileUrls['profilePicUrl'] = profilePicUrl;
      //console.log("profilePicUrl", profilePicUrl);
    }

    
    if (files && files['liscenceImages'] && files['liscenceImages'].length > 0) {
      const licenseImageUrls = [];
      for (const licenseImage of files['liscenceImages']) {
        const licenseImageUrl = await this.uploadService.uploadFiles(licenseImage);
        licenseImageUrls.push(licenseImageUrl);
      }
      uploadedFileUrls['liscenceImages'] = licenseImageUrls;
      //console.log("licenseImageUrls", licenseImageUrls);
    }

    console.log("availability raw", dto.availability);
    console.log("availability type", typeof dto.availability);
    
    // Parse availability if it comes as a string (common with multipart forms)
    let parsedAvailability = dto.availability;
    if (typeof dto.availability === 'string') {
      try {
        parsedAvailability = JSON.parse(dto.availability);
        console.log("availability parsed", parsedAvailability);
      } catch (error) {
        console.error('Error parsing availability JSON:', error);
        // Use default availability structure if parsing fails
        parsedAvailability = {
          monday: { available: false, start: '', end: '' },
          tuesday: { available: false, start: '', end: '' },
          wednesday: { available: false, start: '', end: '' },
          thursday: { available: false, start: '', end: '' },
          friday: { available: false, start: '', end: '' },
          saturday: { available: false, start: '', end: '' },
          sunday: { available: false, start: '', end: '' },
        };
      }
    }
    
    const updateData = { 
      ...dto, 
      ...uploadedFileUrls,
      availability: parsedAvailability
    };

    // Fixed: Move availability inside $set and add returnDocument option
    const updatedUser = await this.userModel.findByIdAndUpdate(
      userId,
      { 
        $set: updateData
      },
      { 
        new: true,
        runValidators: true
      }
    );

    if (!updatedUser) {
      throw new ConflictException('User not found');
    }

    updatedUser.isComplete = true;
    await updatedUser.save();

    return {
      message: 'Profile completed successfully. Please complete your payment setup.',
      user: updatedUser,
    };
  } catch (error) {
    console.error('Error in completeVeterniaProfile:', error);
    throw new ConflictException('Failed to update profile');
  }
}
            
      
      // async completeVeterniaProfile(dto: CompleteVeterniaProfileDto, userId: string,files?: Express.Multer.File[],file?:Express.Multer.File[]) {
      //   try {
      //     console.log("dto",dto)
      //     let uploadedUrls: string[] = [];

         
      //     if (files && files.length > 0) {
      //       uploadedUrls = await this.uploadService.uploadMultipleFiles(files);
      //     }
      //     const profilePicUrl = uploadedUrls.length > 0 ? uploadedUrls[0] : null;
      //     const licenceImageUrls = uploadedUrls.length > 1 ? uploadedUrls.slice(1) : [];
      
      //     const updatedUser = await this.userModel.findByIdAndUpdate(
      //       userId,
      //       {
      //         Specialization: dto.Specialization,
      //         Charges: dto.Charges,
      //         State: dto.State,
      //         Experience: dto.Experience,
      //         // WorkingHours: dto.WorkingHours,
      //         about: dto.about,
      //         availability: dto.availability,
      //         hospital:dto.hospital,
      //         profilePicUrl:profilePicUrl,
      //         isComplete:true,
      //         $push: { liscenceImages: { $each: uploadedUrls } }
      //       },
      //       { new: true }
      //     );
      
      //     if (!updatedUser) {
      //       throw new Error('User not found');
      //     }
          
      //     return {
      //       message: 'Veternia profile completed successfully',
      //       user: updatedUser
      //     };
      //   } catch (error) {
      //     throw new Error(error.message || 'Error completing profile');
      //   }
      // }
    
      // async completeProfile(
      //   dto: CompleteVeterniaProfileDto,
      //   userId: string,
      //   files?: Express.Multer.File[],
      //   file?: Express.Multer.File
      // ) {
      //   try {
      //     let profilePicUrl: string | null = null;
      //     let licenceImageUrls: string[] = [];
      
      //     if (file) {
      //       profilePicUrl = await this.uploadService.uploadFiles(file);
      //     }
      //     if (files && files.length > 0) {
      //       licenceImageUrls = await this.uploadService.uploadMultipleFiles(files);
      //     }
      
      //     const updatedUser = await this.userModel.findByIdAndUpdate(
      //       userId,
      //       {
      //         Specialization: dto.Specialization,
      //         Charges: dto.Charges,
      //         State: dto.State,
      //         Experience: dto.Experience,
      //         about: dto.about,
      //         availability: dto.availability,
      //         hospital: dto.hospital,
      //         profilePicUrl,
      //         isComplete: true,
      //         liscenceImages: { $each: licenceImageUrls } 
      //       },
      //       { new: true }
      //     );
      
      //     if (!updatedUser) {
      //       throw new BadRequestException('User not found');
      //     }
      
      //     return {
      //       message: 'Veternia profile completed successfully',
      //       user: updatedUser
      //     };
      //   } catch (error) {
      //     throw new BadRequestException(error.message || 'Error completing profile');
      //   }
      // }

      async getVeterniaProfile(userId: string) {
        try {
          const user = await this.userModel.findById(userId).select('-password').exec();  
          if (!user) {
            throw new BadRequestException('User not found');
          }

          if(user.userType!=="veternia"){
            throw new BadRequestException('User is not a veternia');
          }

          return user;
        } catch (error) {
          throw new BadRequestException(error.message || 'Error fetching user');
        }
      }

      async getVeterniaProfileById(userId: string) {
        try {
          const user = await this.userModel.findById(userId).select('-password').exec();  
          if (!user) {
            throw new BadRequestException('User not found');
          }
          if(user.userType!=="veternia"){
            throw new BadRequestException('User is not a veternia');
          }

          return user;
        } catch (error) {
          throw new BadRequestException(error.message || 'Error fetching user');
        }
      }
      
      
      async deleteProfile(userId: string) {
        try {
          await this.userModel.findByIdAndDelete(userId);
          console.log("userId",userId)
          if(!userId){
            throw new BadRequestException('User not found');
          }
          return { message: 'User deleted successfully' };
        } catch (error) {
          throw new BadRequestException(error.message || 'Error deleting user');
        }
      
    }

    async getFreeTrialSlots(userId: string) {
      try {
        const user = await this.userModel.findById(userId).select('freeTrialSlots userType').exec();  
        if (!user) {
          throw new BadRequestException('User not found');
        }

        if(user.userType!=="user"){
          console.log("user.userType",user.userType);
          throw new BadRequestException('User is not a pet owner');
        }

        return user.freeTrialSlots;
      } catch (error) {
        throw new BadRequestException(error.message || 'Error fetching user');
      }
    }

      async reduceFreeTrialSlots(userId: string) {
        try {
          const user = await this.userModel.findByIdAndUpdate(userId, { $inc: { freeTrialSlots: -1 } }, { new: true }).exec();  
          if (!user) {
            throw new BadRequestException('User not found');
          }

          if(user.userType!=="user"){
            console.log("user.userType",user.userType)
          throw new BadRequestException('User is not a pet owner');
        }

          return user.freeTrialSlots;
        } catch (error) {
          throw new BadRequestException(error.message || 'Error fetching user');
        }
      }



      
  }

