import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  IsNotEmpty,
  ValidateIf,
  IsOctal,
  IsOptional,
} from 'class-validator';
import { Match } from '../../../shared/decorators/match.decorator';
export class SignupDto {
  @IsString()
  readonly userName: string;

  @IsEmail()
  readonly email: string;

  @MinLength(6)
  readonly password: string;
  @Match('password', { message: 'Confirm password must match password' })
  readonly confirmPass: string;

 // @ValidateIf()
  
  @IsOptional()
  @Matches(/^\+?[0-9]{10,15}$/, {
    message: 'Phone number must be 10 to 15 digits and can start with +',
  })
  readonly phoneNumber: string;

  @IsString()
  readonly userType: string;

 // @ValidateIf(o => o.userType === 'veternia')
 // @IsNotEmpty({ message: 'Specialization is required for veternia' })
 @IsOptional()
  @IsString()
  readonly Specialization?: string;

  // @ValidateIf(o => o.userType === 'veternia')
  // @IsNotEmpty({ message: 'Charges are required for veternia' })
  @IsOptional()
  @IsString()
  readonly Charges?: string;

  // @ValidateIf(o => o.userType === 'veternia')
  // @IsNotEmpty({ message: 'State is required for veternia' })
  @IsOptional()
  @IsString()
  readonly State?: string;

  // @ValidateIf(o => o.userType === 'veternia')
  // @IsNotEmpty({ message: 'Experience is required for veternia' })
  @IsOptional()
  @IsString()
  readonly Experience?: string;

  // @ValidateIf(o => o.userType === 'veternia')
  // @IsNotEmpty({ message: 'WorkingHours are required for veternia' })
  @IsOptional()
  @IsString()
  readonly WorkingHours?: string;

  // @ValidateIf(o => o.userType === 'veternia')
  // @IsNotEmpty({ message: 'About is required for veternia' })
  @IsOptional()
  @IsString()
  readonly about?: string;

  @IsOptional()
  @IsString()
  readonly googleId?: string;

  @IsOptional()
  readonly isGoogleUser?: boolean;

  @IsOptional()
  readonly isVerified?: boolean;

  @IsOptional()
  @IsString()
  readonly profilePicUrl?: string;


}
