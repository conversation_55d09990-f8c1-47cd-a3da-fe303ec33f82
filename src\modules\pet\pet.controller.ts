import { Controller, Post,Put, Body, Get, UseGuards, Req,UseInterceptors,UploadedFile,Param,Query,Delete } from '@nestjs/common';
import { PetService } from './pet.service';
import { createPetDto } from './dto/createPet.dto';
import {JwtAuthGuard}  from '../../shared/guards/jwt-auth.guard';
import { UploadService } from '../upload/upload.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { customRequest } from '../../shared/types/cutomRequest';
import { PetLogDto } from './dto/createPetLogs.dto';
import { Request } from 'express';
import { GetCalendarDataDto } from './dto/getLogDto';
import { BadRequestException } from '@nestjs/common';
import { EditLogDto } from './dto/editLog.dto';

@Controller('pet')
export class PetController {
  constructor(private readonly petService: PetService) {}

  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileInterceptor('file'))
@Post()
async createPet(@Body() createPetDto: createPetDto, @Req() req:customRequest,@UploadedFile() file?: Express.Multer.File,) {
    const userId = req.user['sub']; 
  return this.petService.createPet({ ...createPetDto, ownerId: userId },file);
}

  @Get()
  @UseGuards(JwtAuthGuard)
  async getPets( @Req() req: Request) {
    const userId=req.user['sub'];
    return this.petService.getPets(userId);
  }

  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileInterceptor('file'))
  @Put(':id')
  async editPet(
    @Param('id') petId: string,
    @Body() updatePetDto: Partial<createPetDto>,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    return this.petService.editPet(petId, updatePetDto, file);
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id')
  async getPetById(@Param('id') petId: string) {
    return this.petService.getPetById(petId);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  async deletePet(@Param('id') petId: string) {
    return this.petService.deletePet(petId);
  }
  @UseGuards(JwtAuthGuard)
  @Post('logs')
  async addPetLog(@Body() petLogDto: PetLogDto) {
    return this.petService.addPetLog(petLogDto);
  }

  // ---- New PetLog GET API for a pet ----
  @UseGuards(JwtAuthGuard)
  @Get('logs/:petId/:date')
  async getPetLogs( @Param('petId') petId: string,
  @Param('date') date: string ) {
    return this.petService.getPetLogs(petId,date);
  }

  @UseGuards(JwtAuthGuard)
  @Get('getCalenderData/:petId')
  async getLogCalender( @Param('petId') petId:string,  @Query() query: GetCalendarDataDto){
    if (!petId) {
      throw new BadRequestException('petId is required');
    }
    const dateObj=new Date(query.date);
    console.log("date obj",dateObj)
    return this.petService.getCalenderData(petId,dateObj);
  }

  @UseGuards(JwtAuthGuard)
@Put('editLog/:logId')
async editLogs(
  @Param('logId') logId: string,
  @Body() editLogDto: EditLogDto,
) {
  return this.petService.editLogs(logId, editLogDto);
}
@UseGuards(JwtAuthGuard)
@Get('/getPetLogById/:id')
async getPetLogById(@Param('id') id: string){
  return this.petService.getPetLogById(id)
}
}
