import { IsString, <PERSON>NotEmpty, IsOptional, IsDateString,IsMongoId } from 'class-validator';

export class CreateBookingDto {
    @IsMongoId({ message: 'veterniaId must be a valid MongoDB ObjectId' })
    veterniaId: string;
  
    @IsMongoId({ message: 'petId must be a valid MongoDB ObjectId' })
    petId: string;

  @IsDateString({}, { message: 'Date must be a valid ISO date string (YYYY-MM-DD)' })
  @IsOptional()
  date?: string;

  @IsString()
  @IsNotEmpty()
  startTime: string;

  @IsString()
  @IsNotEmpty()
  endTime: string; 

  @IsString()
  @IsOptional()
  paymentMethodId?: string;

}
