<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Stripe Payment</title>
    <script src="https://js.stripe.com/v3/"></script>
</head>
<body>
    <form id="payment-form">
        <div id="card-element"><!-- Stripe.js injects the Card Element --></div>
        <button id="submit">Submit Payment</button>
    </form>
    <script>
        const stripe = Stripe('pk_test_51Rfnde2XCV0AWrFEKl3Je6t6ekLrt4bXu9xCyloMXYycqpWtq67Adl8ZXKIClVVrt4xIkk0c0B02TGKtOmNz6u8b00lT08zoXc');
        const elements = stripe.elements();
        const cardElement = elements.create('card');
        cardElement.mount('#card-element');

        const form = document.getElementById('payment-form');
        form.addEventListener('submit', async (event) => {
            event.preventDefault();
            const { paymentMethod, error } = await stripe.createPaymentMethod({
                type: 'card',
                card: cardElement,
            });
            console.log("🚀 ~ form.addEventListener ~ paymentMethod:", paymentMethod)

            if (error) {
                console.error(error);
            } else {
                // Send paymentMethod.id to your server
                const response = await fetch('http://localhost:4000/payments/create-payment-intent', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************.OFlgj3GVnR8UMmraITFqaShZvBNm4emeqIFa_Zb-Xa0` // Include your access token
                    },
                    body: JSON.stringify({
                        "userId"    :   "68c298afaa7013176ad45143",
                        "amount"    :   "10",
                        "currency"  :  "usd",
                        "paymentMethodId" : paymentMethod.id
})
                });

                const result = await response.json();
                console.log(result);

            // Confirm the PaymentIntent using the client_secret returned from the backend
            const { clientSecret } = result;

            // Confirm the payment on the frontend
            const { paymentIntent, confirmError } = await stripe.confirmCardPayment(clientSecret, {
                payment_method: paymentMethod.id,
            });

            if (confirmError) {
                console.error('Payment confirmation failed:', confirmError.message);
            } else if (paymentIntent.status === 'succeeded') {
                console.log('Payment successful!');
    }
            }
        });
    </script>
</body>
</html>
