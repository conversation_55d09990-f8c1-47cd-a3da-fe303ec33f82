import {
    IsString,
    IsOptional,
    IsBoolean,
    ValidateNested,
  } from 'class-validator';
  import { Transform, Type,plainToInstance } from 'class-transformer';
  
  
  class DayAvailabilityDto {
    @IsBoolean()
    @IsOptional()
    available?: boolean;  // Make optional to avoid validation errors when missing
  
    @IsString()
    @IsOptional()
    start?: string;       // Optional for the same reason
  
    @IsString()
    @IsOptional()
    end?: string;         // Optional
  }
  
  class AvailabilityDto {
    @IsOptional()
    @ValidateNested()
    @Type(() => DayAvailabilityDto)
    monday?: DayAvailabilityDto;
  
    @IsOptional()
    @ValidateNested()
    @Type(() => DayAvailabilityDto)
    tuesday?: DayAvailabilityDto;
  
    @IsOptional()
    @ValidateNested()
    @Type(() => DayAvailabilityDto)
    wednesday?: DayAvailabilityDto;
  
    @IsOptional()
    @ValidateNested()
    @Type(() => DayAvailabilityDto)
    thursday?: DayAvailabilityDto;
  
    @IsOptional()
    @ValidateNested()
    @Type(() => DayAvailabilityDto)
    friday?: DayAvailabilityDto;
  
    @IsOptional()
    @ValidateNested()
    @Type(() => DayAvailabilityDto)
    saturday?: DayAvailabilityDto;
  
    @IsOptional()
    @ValidateNested()
    @Type(() => DayAvailabilityDto)
    sunday?: DayAvailabilityDto;
  }
  
  export class CompleteVeterniaProfileDto {
    @IsString()
    Specialization: string;
  
    @IsString()
    Charges: string;
  
    @IsString()
    State: string;
  
    @IsString()
    Experience: string;
  
    // @IsString()
    // WorkingHours: string;
  
    @IsString()
    about: string;
  
    @IsOptional()
    @ValidateNested()
    @Transform(({ value }) => {
      console.log('Transform called with:', value);
      if (typeof value === 'string') {
        try {
        const parsed=JSON.parse(value);
          return plainToInstance(AvailabilityDto, parsed);
        } catch {

          throw new Error('Invalid JSON for availability');
        }
      }
      return value;
    })
    @Type(() => AvailabilityDto)
    availability?: AvailabilityDto;  // Mark optional explicitly
    @IsString()
    hospital: string
  }
  