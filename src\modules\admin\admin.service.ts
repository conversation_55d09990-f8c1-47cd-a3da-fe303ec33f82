import { Injectable, NotFoundException,BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Banner, BannerDocument } from './schemas/bannerSchema';
import { CreateBannerDto } from './dto/create_banner.dto';
import { UploadService } from '../upload/upload.service';
import { UsersModule } from '../users/users.module';
import { User,UserDocument } from '../users/schemas/user.schema';
import * as bcrypt from 'bcrypt';

@Injectable()
export class AdminService {
  constructor(
    @InjectModel(Banner.name) private bannerModel: Model<BannerDocument>,
    @InjectModel(User.name) private userModel:Model<UserDocument>,
    private readonly uploadService: UploadService,
   

  ) {}
  async getAllUsers(dto) {
    try {
      const page = dto.page ? parseInt(dto.page, 10) : 1;
      const limit = dto.limit ? parseInt(dto.limit, 10) : 10;
      const skip = (page - 1) * limit;
  
      const findUsers = await this.userModel
        .find({ userType: "user" }) // filter only "user" type
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }).select('-password'); 
  
      const totalUsers = await this.userModel.countDocuments({ userType: "user" });
      console.log("total Users = ",totalUsers)
  
      return {
        total: totalUsers,
        page,
        limit,
        users: findUsers,
      };
    } catch (error) {
      throw new Error(error.message || "Error fetching users");
    }
  }
  

async getAllVeternia(dto) {
  try {
    const page = dto.page ? parseInt(dto.page, 10) : 1;
    const limit = dto.limit ? parseInt(dto.limit, 10) : 10;
    const skip = (page - 1) * limit;

    const filter = {
      userType: 'veternia',
      isCompletedPaymentSetup: true,
      isComplete: true
    };

    const findVets = await this.userModel.find(filter)
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 })
      .select('-password'); // optional: newest first
    
    const totalVets = await this.userModel.countDocuments(filter);
    
    return {
      success: true,
      total: totalVets,
      page,
      limit,
      data: findVets
    };
  } catch (error) {
    return {
      success: false,
      message: error.message
    };
  }
}
  
  async getUserById(userId: string) {
    try {
      const user = await this.userModel.findById(userId).select('-password');
      if (!user) {
        throw new Error('User not found');
      }
      return user;
    } catch (error) {
      throw new Error(`Error fetching user by ID: ${error.message}`);
    }
  }
  
  async getDashBoardData() {
    try {
      const totalAdmins = await this.userModel.countDocuments({ userType: 'admin' });
      const totalUsers = await this.userModel.countDocuments({ userType: 'user' });
      const totalVets = await this.userModel.countDocuments({ userType: 'veternia' });
      const totalAdminCount = await this.userModel.countDocuments({ userType: 'admin' });
      const allUsersData = await this.userModel.find({userType:'user'}).select('createdAt');
  
      return {
        success: true,
        totalAdmins: totalAdmins,
        totalAdminCount: totalAdminCount,
        totalUsers:totalUsers,
        totalVets:totalVets,
        allUsersData,
      };
    } catch (error) {
      throw new Error(`Error fetching dashboard data: ${error.message}`);
    }
  }
  
  async blockUnBlockUser(userId: string) {
    try {
      const user = await this.userModel.findById(userId);
      if (!user) {
        throw new NotFoundException('User not found');
      }

      if(user.userType === 'admin') {
        throw new BadRequestException('Cannot block admin');
      }
  
      user.blockStatus = !user.blockStatus; // Toggle value
      await user.save();
  
      return {
        success: true,
        message: `User has been ${user.blockStatus ? 'blocked' : 'unblocked'} successfully`,
        data: user,
      };
    } catch (error) {
      throw new Error(error.message);
    }
  }
  
  async approveDisapproveVet(vetId: string) {
    try {
      const vet = await this.userModel.findById(vetId);
      if (!vet) {
        throw new NotFoundException('Veterinarian not found');
      }

      if(vet.userType !== 'veternia') {
        throw new BadRequestException('Cannot approve non-veterinarian');
      }
  
      vet.approve = !vet.approve;
      await vet.save();
  
      return {
        success: true,
        message: `Veterinarian has been ${vet.approve ? 'approved' : 'disapproved'} successfully`,
        data: vet,
      };
    } catch (error) {
      throw new Error(error.message);
    }
  }
  
  async createBanner(
    createBannerDto: CreateBannerDto,
    file?: Express.Multer.File,
  ): Promise<object> {
    try {
      if (file) {
        const fileUrl = await this.uploadService.uploadFiles(file);
        createBannerDto.image = fileUrl;
      }

      const newBanner = new this.bannerModel(createBannerDto);
      await newBanner.save();

      return newBanner;
    } catch (error) {
      console.error('Error creating banner:', error);
      throw new Error('Failed to create banner');
    }
  }

  async getAllBanners(): Promise<Banner[]> {
    return this.bannerModel.find().exec();
  }

  async editBanner(
    bannerId: string,
    updateDto: CreateBannerDto,
    file?: Express.Multer.File,
  ): Promise<object> {
    const banner = await this.bannerModel.findById(bannerId);
    if (!banner) throw new NotFoundException('Banner not found');

    if (file) {
      const imageUrl = await this.uploadService.uploadFiles(file);
      updateDto.image = imageUrl;
    }

   const getBanner= await this.bannerModel.findByIdAndUpdate(bannerId, updateDto, { new: true });

    return getBanner;
  }

  async deleteBanner(bannerId: string): Promise<string> {
    const deleted = await this.bannerModel.findByIdAndDelete(bannerId);
    if (!deleted) throw new NotFoundException('Banner not found');

    return 'Banner deleted successfully';
  }


  //----------------- CRUD for Admin ---------------------------------------

  async addAdmin(createUserDto): Promise<UserDocument> {
    const existingUser = await this.userModel.findOne({ email: createUserDto.email });
    if (existingUser) {
      // I want to return the erorr message to api call
      throw new Error('User already exists');
    }

    if(!createUserDto.password){
      throw new Error('Password is required');
    }
    if(!createUserDto.confirmPass){
      throw new Error('Confirm password is required');
    }
    if(!createUserDto.userName){
      throw new Error('User name is required');
    }
    if(!createUserDto.email){
      throw new Error('Email is required');
    }

    if(createUserDto.password !== createUserDto.confirmPass) {
      throw new Error('Password and confirm password do not match');
    }
    
    createUserDto.userType = 'admin';
    createUserDto.isVerified = true;
    createUserDto.blockStatus = false;
    createUserDto.approve = true;
    createUserDto.isComplete = true;

    const hashed = await bcrypt.hash(createUserDto.password, 10);
    createUserDto.password = hashed;
    const user = new this.userModel(createUserDto);
    await user.save();
    return user;
  }

  async editAdmin(adminId: string, updateDto: any): Promise<UserDocument> {
    const admin = await this.userModel.findById(adminId);
    if (!admin) throw new NotFoundException('Admin not found');
    
    if (updateDto.password && updateDto.password !== updateDto.confirmPass) {
      throw new Error('Password and confirm password do not match');
    }
    if (updateDto.password) {
      const hashed = await bcrypt.hash(updateDto.password, 10);
      updateDto.password = hashed;
    }

    try {
      
    return await this.userModel.findByIdAndUpdate(adminId, updateDto, { new: true });

    } catch (error) {
    throw new BadRequestException('Failed to update admin');
    }
  }

  async getAllAdmin(dto) {
    try {
      const page = dto.page ? parseInt(dto.page, 10) : 1;
      const limit = dto.limit ? parseInt(dto.limit, 10) : 10;
      const skip = (page - 1) * limit;
      const totalAdmins = await this.userModel.countDocuments({ userType: "admin" });
      const admins = await this.userModel
        .find({ userType: "admin" }) // filter only "user" type
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }).select('-password'); 
  
     
  
      return {
        total: totalAdmins,
        page,
        limit,
        admins: admins,
      };
    } catch (error) {
      throw new Error(error.message || "Error fetching users");
    }
  }

  async getAdminById(adminId: string) {
    try {
      const admin = await this.userModel.find({ _id: adminId ,userType:"admin"}).select('-password');
      if (!admin) {
        throw new Error('Admin not found');
      }
      return admin;
    } catch (error) {
      throw new Error(`Error fetching admin by ID: ${error.message}`);
    }
  }

  async deleteAdmin(adminId: string): Promise<string> {

    try{
      const deleted = await this.userModel.findByIdAndDelete(adminId);
    if (!deleted) throw new NotFoundException('Admin not found');
    return 'Admin deleted successfully';
  }catch(error){
      throw new BadRequestException('Failed to delete admin');
    }
  }
}
