import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { booking, bookingDocument } from '../booking/schemas/bookingSchemas';
import { VideoCall, VideoCallDocument } from '../videoCalling/schemas/video-call.schema';
import { User, UserDocument } from '../users/schemas/user.schema';

@Injectable()
export class CronService {
  private readonly logger = new Logger(CronService.name);

  constructor(
    @InjectModel(booking.name) private readonly bookingModel: Model<bookingDocument>,
    @InjectModel(VideoCall.name) private readonly videoCallModel: Model<VideoCallDocument>,
    @InjectModel(User.name) private readonly userModel: Model<UserDocument>,
  ) {}

  /**
   * <PERSON>ron job that runs every 5 minutes to check for ended bookings
   * and update their status based on video call participation
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async handleBookingCompletion() {
    this.logger.log('Running booking completion check...');

    try {
      // Get current time
      const now = new Date();

      // Find all active bookings that have ended (endTime < now)
      const endedBookings = await this.bookingModel.find({
        bookingStatus: 'active',
        endTime: { $lt: now }
      }).populate('veterniaId');

      this.logger.log(`Found ${endedBookings.length} ended bookings to process`);

      for (const booking of endedBookings) {
        await this.processEndedBooking(booking);
      }

      this.logger.log('Booking completion check completed');
    } catch (error) {
      this.logger.error('Error in booking completion cron job:', error);
    }
  }

  /**
   * Process an individual ended booking
   */
  private async processEndedBooking(booking: bookingDocument) {
    try {
      this.logger.log(`Processing booking ${booking._id} with channelName: ${booking.channelName}`);

      // Check if booking has a channelName
      if (!booking.channelName) {
        this.logger.warn(`Booking ${booking._id} has no channelName, marking as incomplete`);
        await this.updateBookingStatus(booking._id.toString(), 'incomplete');
        return;
      }

      // Find the corresponding video call record
      const videoCall = await this.videoCallModel.findOne({
        channelName: booking.channelName
      });

      if (!videoCall) {
        this.logger.warn(`No video call found for channelName: ${booking.channelName}, marking booking as incomplete`);
        await this.updateBookingStatus(booking._id.toString(), 'incomplete');
        return;
      }

      // Check if both user and veterinarian joined the call
      const bothJoined = videoCall.isUserJoinedCall && videoCall.isVeterniaJoinedCall;

      if (bothJoined) {
        this.logger.log(`Both parties joined call for booking ${booking._id}, marking as completed`);
        await this.updateBookingStatus(booking._id.toString(), 'completed');
        
        // Update veterinarian earnings
        await this.updateVeterinarianEarnings(booking);
      } else {
        this.logger.log(`Not both parties joined call for booking ${booking._id} (user: ${videoCall.isUserJoinedCall}, vet: ${videoCall.isVeterniaJoinedCall}), marking as incomplete`);
        await this.updateBookingStatus(booking._id.toString(), 'incomplete');
      }

    } catch (error) {
      this.logger.error(`Error processing booking ${booking._id}:`, error);
    }
  }

  /**
   * Update booking status
   */
  private async updateBookingStatus(bookingId: string, status: 'completed' | 'incomplete') {
    try {
      await this.bookingModel.findByIdAndUpdate(bookingId, {
        bookingStatus: status
      });
      this.logger.log(`Updated booking ${bookingId} status to ${status}`);
    } catch (error) {
      this.logger.error(`Error updating booking ${bookingId} status:`, error);
      throw error;
    }
  }

  /**
   * Update veterinarian earnings when booking is completed
   */
  private async updateVeterinarianEarnings(booking: bookingDocument) {
    try {
      const veterinarianId = booking.veterniaId;
      const amountCharged = booking.amountCharged;

      if (!amountCharged || amountCharged <= 0) {
        this.logger.warn(`Booking ${booking._id} has no amount charged, skipping earnings update`);
        return;
      }

      // Update veterinarian's earnings
      const updateResult = await this.userModel.findByIdAndUpdate(
        veterinarianId,
        {
          $inc: {
            total_earnings: amountCharged,
            allowed_withdrawl_balance: amountCharged
          }
        },
        { new: true }
      );

      if (updateResult) {
        this.logger.log(`Updated veterinarian ${veterinarianId} earnings by $${amountCharged}. New total: $${updateResult.total_earnings}, Available: $${updateResult.allowed_withdrawl_balance}`);
      } else {
        this.logger.error(`Failed to find veterinarian ${veterinarianId} for earnings update`);
      }

    } catch (error) {
      this.logger.error(`Error updating veterinarian earnings for booking ${booking._id}:`, error);
      throw error;
    }
  }

  /**
   * Manual method to trigger booking completion check (for testing)
   */
  async triggerBookingCompletionCheck() {
    this.logger.log('Manually triggering booking completion check...');
    await this.handleBookingCompletion();
  }
}
