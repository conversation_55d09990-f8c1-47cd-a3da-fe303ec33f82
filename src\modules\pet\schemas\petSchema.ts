import { <PERSON>p,Schema,SchemaFactory } from "@nestjs/mongoose";
import { Type } from "class-transformer";
import { Document as MongooseDocument ,Types} from "mongoose";

export type petDocument = MongooseDocument & Pet;

@Schema({timestamps:true})
export class Pet{

    @Prop({type:Types.ObjectId,ref:'User'})
    ownerId: Types.ObjectId
    @Prop({type:[Types.ObjectId],ref:'PetLog'})
    petLogs: Types.ObjectId[]

    @Prop()
    petName: String

    @Prop()
    age: String

    @Prop()
    breed: String

    @Prop()
    health: String
    @Prop()
    image: String

    @Prop()
    healthStats: Number
    
    @Prop()
    meditation: String

    

}
export const petSchema=SchemaFactory.createForClass(Pet)