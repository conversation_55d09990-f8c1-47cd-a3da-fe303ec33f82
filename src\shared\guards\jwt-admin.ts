// src/shared/guards/jwt-admin.guard.ts
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtAuthGuard } from './jwt-auth.guard';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtAdminGuard extends JwtAuthGuard implements CanActivate {
  constructor(configService: ConfigService) {
    super(configService);
  }

  canActivate(context: ExecutionContext): boolean {
    // First verify the JWT token using parent guard
    const isValidToken = super.canActivate(context);
    
    if (!isValidToken) {
      return false;
    }


    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || user.userType !== 'admin') {
      throw new ForbiddenException('Access denied. Admin role required.');
    }

    return true;
  }
}
