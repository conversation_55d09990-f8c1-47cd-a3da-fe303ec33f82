// import { stripe } from "./index.js";
// import { Order } from "./models/Order.js";
// import { handleClearCart } from "./services/cart.js";
// import User from "./models/User.js";
// import { transporter } from "./utils/nodeMailer.js";
// export const createSession = async (req, res) => {
//   try {
//     const { items, deliveryAddress } = req.body;

//     const lineItems = items.map((item) => ({
//       price_data: {
//         currency: "usd",
//         product_data: { name: item.name },
//         unit_amount: item.price * 100,
//       },
//       quantity: item.quantity,
//     }));

//     const totalAmount = items.reduce(
//       (sum, item) => sum + item.price * item.quantity,
//       0
//     );

//     const order = await Order.create({
//       userId: req.user.id,
//       items,
//       totalAmount,
//       deliveryAddress,
//       paymentStatus: "pending",
//     });
//     const metadata = {
//       userId: req.user.id.toString(),
//       orderId: order._id.toString(),
//     };
//     const session = await stripe.checkout.sessions.create({
//       payment_method_types: ["card", "paypal"],
//       line_items: lineItems,
//       mode: "payment",
//       success_url: process.env.STRIPE_SUCCESS_REDIRECT,
//       cancel_url: process.env.STRIPE_FAILURE_REDIRECT,
//       expand: ["payment_intent"],
//       metadata,
//     });

//     res.json({ sessionUrl: session.url });
//   } catch (error) {
//     console.error("Session creation error:", error.message);
//     res.status(500).json({ message: error.message });
//   }
// };

// export const webhook = async (req, res) => {
//   const sig = req.headers["stripe-signature"];
//   const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

//   let event;

//   try {
//     event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);

//     if (event.type === "checkout.session.completed") {
//       const session = event.data.object;
//       const metaData = session.metadata;
//       if (session.payment_intent) {
//         try {
//           const paymentIntent = await stripe.paymentIntents.retrieve(
//             session.payment_intent
//           );
//           const paymentMethod = await stripe.paymentMethods.retrieve(
//             paymentIntent.payment_method
//           );
//           const paymentMethodType = paymentMethod.type;
//           await handleClearCart(metaData.userId);
//           const order = await Order.findByIdAndUpdate(metaData.orderId, {
//             paymentIntentId: paymentIntent.id,
//             paymentStatus: "succeeded",
//             paymentMethod: paymentMethodType,
//           });
//           const user = await User.findById(metaData.userId);
//           if (user?.email) {
//             const mailOptions = {
//               from: process.env.EMAIL_USER,
//               to: user.email,
//               subject: "Order Confirmation on Drive-X",
//               text: `Hi ${user.firstName} ${user.lastName},\n\nYour payment of $${order.totalAmount} was successful.\n\nOrder No: ${order.order_no}\n\nThank you for shopping with us! `,
//             };

//             await transporter.sendMail(mailOptions);
//           }
//         } catch (err) {
//           console.warn("Failed to retrieve PaymentIntent:", err.message);
//         }
//       }
//     }

//     res.status(200).send("Event received");
//   } catch (err) {
//     console.error("Webhook error:", err.message);
//     res.status(400).send(`Webhook Error: ${err.message}`);
//   }
// };