import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";

export type BannerDocument = Banner & Document;

@Schema({ timestamps: true })
export class Banner {
  @Prop({ required: true })
  percentage: number;

  @Prop({ required: true })
  image: string;

  @Prop({ required: true })
  webSiteUrl: string;
}

export const BannerSchema = SchemaFactory.createForClass(Banner);
