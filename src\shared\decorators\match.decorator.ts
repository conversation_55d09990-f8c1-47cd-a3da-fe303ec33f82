// src/common/decorators/match.decorator.ts
import {
    registerDecorator,
    ValidationOptions,
    ValidationArguments,
  } from 'class-validator';
  
  export function Match(property: string, validationOptions?: ValidationOptions) {
    return function (object: any, propertyName: string) {
      registerDecorator({
        name: 'Match',
        target: object.constructor,
        propertyName,
        options: validationOptions,
        constraints: [property],
        validator: {
          validate(value: any, args: ValidationArguments) {
            const [relatedPropertyName] = args.constraints;
            const relatedValue = (args.object as any)[relatedPropertyName];
            return value === relatedValue;
          },
          defaultMessage(args: ValidationArguments) {
            return `${args.property} must match ${args.constraints[0]}`;
          },
        },
      });
    };
  }
  