import { IsOptional, IsString, IsEmail, IsBoolean, IsIn } from 'class-validator';
import { Prop } from '@nestjs/mongoose';

export class updateVeterniaDto {

    @IsOptional()
    @IsString()
    userName?: string;
    

    @IsOptional()
    @IsString()
    phoneNumber?: string;

    @IsOptional()
    @IsString()
    profilePicUrl?: string;

    @IsOptional()
    @IsString()
    Specialization?: string;

    @IsOptional()
    @IsString()
    hospital?: string;

    @IsOptional()
    @IsString()
    Charges?: string;
    @IsOptional()
    @IsString()
    State?: string;

    @IsOptional()
    @IsString()
    Experience?: string;

    @IsOptional()
    @IsString()
    about?: string;

    @IsOptional()
    @IsString()
    licenceImages?: string[];

    @IsOptional()
    @IsString({ each: true })
    licenceImagesToDelete?: string[];

    // @IsOptional()
    // @IsString()
    // licenceImagesToDelete?: string[];

    @IsOptional()
    @Prop({
            type: Object,
            default: {
              monday: { available: false, start: '', end: '' },
              tuesday: { available: false, start: '', end: '' },
              wednesday: { available: false, start: '', end: '' },
              thursday: { available: false, start: '', end: '' },
              friday: { available: false, start: '', end: '' },
              saturday: { available: false, start: '', end: '' },
              sunday: { available: false, start: '', end: '' },
            }
          })
          availability: {
            monday: { available: boolean; start: string; end: string };
            tuesday: { available: boolean; start: string; end: string };
            wednesday: { available: boolean; start: string; end: string };
            thursday: { available: boolean; start: string; end: string };
            friday: { available: boolean; start: string; end: string };
            saturday: { available: boolean; start: string; end: string };
            sunday: { available: boolean; start: string; end: string };
          };
}
