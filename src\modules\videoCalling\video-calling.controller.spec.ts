import { Test, TestingModule } from '@nestjs/testing';
import { VideoCallingController } from './video-calling.controller';
import { VideoCallingService } from './video-calling.service';

describe('VideoCallingController', () => {
  let controller: VideoCallingController;
  let service: VideoCallingService;

  const mockVideoCallingService = {
    createVideoCall: jest.fn(),
    joinVideoCall: jest.fn(),
    endVideoCall: jest.fn(),
    getVideoCallHistory: jest.fn(),
    getActiveVideoCall: jest.fn()
  };

  const mockVideoCall = {
    _id: 'mockId',
    appId: 'testAppId',
    channelName: 'test-channel',
    token: 'test-token',
    userId: 'user123',
    veterinarianId: 'vet456',
    status: 'scheduled',
    startTime: new Date()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [VideoCallingController],
      providers: [
        {
          provide: VideoCallingService,
          useValue: mockVideoCallingService
        }
      ]
    }).compile();

    controller = module.get<VideoCallingController>(VideoCallingController);
    service = module.get<VideoCallingService>(VideoCallingService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createVideoCall', () => {
    it('should create a video call', async () => {
      const createDto = {
        channelName: 'test-channel',
        userId: 'user123',
        veterinarianId: 'vet456'
      };

      mockVideoCallingService.createVideoCall.mockResolvedValue(mockVideoCall);

      const result = await controller.createVideoCall(createDto);

      expect(service.createVideoCall).toHaveBeenCalledWith(createDto);
      expect(result).toEqual(mockVideoCall);
    });
  });

  describe('joinVideoCall', () => {
    it('should join a video call', async () => {
      const joinDto = {
        channelName: 'test-channel',
        userId: 'user123'
      };

      const joinResponse = {
        token: 'generated-token',
        appId: 'test-app-id',
        channelName: 'test-channel'
      };

      mockVideoCallingService.joinVideoCall.mockResolvedValue(joinResponse);

      const result = await controller.joinVideoCall(joinDto, 'user123');

      expect(service.joinVideoCall).toHaveBeenCalledWith(joinDto);
      expect(result).toEqual(joinResponse);
    });
  });

  describe('endVideoCall', () => {
    it('should end a video call', async () => {
      const endDto = {
        channelName: 'test-channel',
        duration: 30
      };

      const endResponse = { message: 'Video call ended successfully' };

      mockVideoCallingService.endVideoCall.mockResolvedValue(endResponse);

      const result = await controller.endVideoCall(endDto);

      expect(service.endVideoCall).toHaveBeenCalledWith(endDto);
      expect(result).toEqual(endResponse);
    });
  });

  describe('getVideoCallHistory', () => {
    it('should get video call history', async () => {
      const mockHistory = [mockVideoCall];
      mockVideoCallingService.getVideoCallHistory.mockResolvedValue(mockHistory);

      const result = await controller.getVideoCallHistory('user123');

      expect(service.getVideoCallHistory).toHaveBeenCalledWith('user123');
      expect(result).toEqual(mockHistory);
    });

    it('should get video call history without userId', async () => {
      const mockHistory = [mockVideoCall];
      mockVideoCallingService.getVideoCallHistory.mockResolvedValue(mockHistory);

      const result = await controller.getVideoCallHistory();

      expect(service.getVideoCallHistory).toHaveBeenCalledWith(undefined);
      expect(result).toEqual(mockHistory);
    });
  });

  describe('getActiveVideoCall', () => {
    it('should get active video call', async () => {
      const activeCall = { ...mockVideoCall, status: 'active' };
      mockVideoCallingService.getActiveVideoCall.mockResolvedValue(activeCall);

      const result = await controller.getActiveVideoCall('test-channel');

      expect(service.getActiveVideoCall).toHaveBeenCalledWith('test-channel');
      expect(result).toEqual(activeCall);
    });
  });

  describe('generateToken', () => {
    it('should generate token for video call', async () => {
      const tokenResponse = {
        token: 'generated-token',
        appId: 'test-app-id',
        channelName: 'test-channel'
      };

      mockVideoCallingService.joinVideoCall.mockResolvedValue(tokenResponse);

      const result = await controller.generateToken('test-channel', 'user123');

      expect(service.joinVideoCall).toHaveBeenCalledWith({
        channelName: 'test-channel',
        userId: 'user123'
      });
      expect(result).toEqual(tokenResponse);
    });
  });
});
