import { Modu<PERSON> } from '@nestjs/common';
import { PetService } from './pet.service';
import { PetController } from './pet.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Pet, petSchema } from './schemas/petSchema';
import { UploadModule } from '../upload/upload.module';
import { PetLog, PetLogSchema } from './schemas/petLogSchema';
import { booking,bookingSchema } from '../booking/schemas/bookingSchemas';
import { User,UserSchema } from '../users/schemas/user.schema';

@Module({
  imports:[MongooseModule.forFeature([{name:Pet.name,schema:petSchema},{name:PetLog.name,schema:PetLogSchema},
   /*/ {name:booking.name,schema:bookingSchema}/*/
   { name: booking.name, schema: bookingSchema }, // Add booking model here
   { name: User.name, schema: UserSchema }
  ]),UploadModule],
  providers: [PetService],
  controllers: [PetController]
})
export class PetModule {}
