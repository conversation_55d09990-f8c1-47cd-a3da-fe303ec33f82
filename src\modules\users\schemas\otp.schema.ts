import {<PERSON>p,Schema,SchemaFactory} from "@nestjs/mongoose"

import { Document as MongooseDocument, Types } from "mongoose"

export type OtpDocument=  Otp & MongooseDocument;

@Schema({timestamps:true})
export class Otp{
    @Prop({type:Types.ObjectId,ref:'User',required:true})
    userId:Types.ObjectId

    @Prop({required: true})
    otp: string

    @Prop({
        required: true,
        default: () => new Date(Date.now() + 2 * 60 * 1000), 
      })
    expireAt:Date
}
export const OtpSchema=SchemaFactory.createForClass(Otp);