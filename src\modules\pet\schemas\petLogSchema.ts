import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type PetLogDocument = Document & PetLog;

@Schema({ timestamps: true })
export class PetLog {
  @Prop({ type: Types.ObjectId, ref: 'Pet', required: true })
  petId: Types.ObjectId;

  @Prop({ type: Number })
  health: number;

  @Prop({ type: Boolean })
  eating: boolean;

  @Prop({ type: Boolean })
  normalP: boolean;

  @Prop({ type: Boolean })
  sleepCycle: boolean;

  @Prop({ type: Boolean })
  movedE: boolean;

  @Prop({ type: Boolean })
  tookMed: boolean;

  @Prop({})
  behavious: string

  @Prop({})
  physicalSymptoms: string

  @Prop({ type: Date, default: () => new Date() })
  date: Date;

}

export const PetLogSchema = SchemaFactory.createForClass(PetLog);
