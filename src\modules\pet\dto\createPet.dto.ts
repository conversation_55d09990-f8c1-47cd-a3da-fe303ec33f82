import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,IsOptional } from "class-validator";
import { Type } from "class-transformer";

export class createPetDto {
  @IsOptional()
  @IsString()
  readonly ownerId: string;

  @IsString()
  readonly petName: string;

  @IsString()
  @Type(() => String)
  readonly age: string;

  @IsString()
  readonly health: string;

  @IsString()
  readonly meditation: string;

  @IsString()
  readonly breed: string;

  @IsOptional()
  @IsString()
  image?:string
}
