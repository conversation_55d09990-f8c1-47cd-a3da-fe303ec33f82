import { 
  <PERSON>, 
  Get, 
  Param, 
  Query, 
  UseGuards, 
  Request,
  BadRequestException,
  NotFoundException 
} from '@nestjs/common';
import { WithdrawalsService } from './withdrawals.service';
import { JwtAuthGuard } from '../../shared/guards/jwt-auth.guard';
import { JwtVeterniaGuard } from 'src/shared/guards/jwt-veternia';

@Controller('withdrawals')

export class WithdrawalsController {
  constructor(private readonly withdrawalsService: WithdrawalsService) {}

  /**
   * Get all withdrawals for the authenticated user
   */
  // @UseGuards(JwtVeterniaGuard)
  // @Get('my-withdrawals')
  // async getMyWithdrawals(@Request() req) {
  //   try {
  //     const userId = req.user['sub'];

  //     const withdrawals = await this.withdrawalsService.getUserWithdrawals(userId);
  //     return {
  //       success: true,
  //       message: 'Withdrawals retrieved successfully',
  //       data: withdrawals
  //     };
  //   } catch (error) {
  //     throw new BadRequestException({
  //       success: false,
  //       message: 'Failed to retrieve withdrawals',
  //       error: error.message
  //     });
  //   }
  // }

  @UseGuards(JwtVeterniaGuard)
  @Get('my-withdrawals')
  async getMyWithdrawals(@Request() req) {
    try {
      const userId = req.user['sub'];
      const withdrawals = await this.withdrawalsService.getUserWithdrawals(userId);
      return {
        success: true,
        message: 'Withdrawals retrieved successfully',
        data: withdrawals
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Failed to retrieve withdrawals',
        error: error.message
      });
    }
  }

  /**
   * Get withdrawal statistics for the authenticated user
   */
  @Get('my-stats')
  async getMyWithdrawalStats(@Request() req) {
    try {
      const userId = req.user.userId || req.user.id;
      if (!userId) {
        throw new BadRequestException('User ID not found in request');
      }

      const stats = await this.withdrawalsService.getUserWithdrawalStats(userId);
      return {
        success: true,
        message: 'Withdrawal statistics retrieved successfully',
        data: stats
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Failed to retrieve withdrawal statistics',
        error: error.message
      });
    }
  }

  /**
   * Get withdrawals for a specific user (admin only - you may want to add admin guard)
   */
  @Get('user/:userId')
  async getUserWithdrawals(@Param('userId') userId: string) {
    try {
      const withdrawals = await this.withdrawalsService.getUserWithdrawals(userId);
      return {
        success: true,
        message: 'User withdrawals retrieved successfully',
        data: withdrawals
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException({
        success: false,
        message: 'Failed to retrieve user withdrawals',
        error: error.message
      });
    }
  }

  /**
   * Get withdrawal by payout ID
   */
  @Get('payout/:payoutId')
  async getWithdrawalByPayoutId(@Param('payoutId') payoutId: string) {
    try {
      const withdrawal = await this.withdrawalsService.getWithdrawalByPayoutId(payoutId);
      if (!withdrawal) {
        throw new NotFoundException('Withdrawal not found');
      }

      return {
        success: true,
        message: 'Withdrawal retrieved successfully',
        data: withdrawal
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException({
        success: false,
        message: 'Failed to retrieve withdrawal',
        error: error.message
      });
    }
  }

  /**
   * Get all withdrawals with pagination (admin only - you may want to add admin guard)
   */
  @Get('all')
  async getAllWithdrawals(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10'
  ) {
    try {
      const pageNum = parseInt(page, 10);
      const limitNum = parseInt(limit, 10);

      if (pageNum < 1 || limitNum < 1) {
        throw new BadRequestException('Page and limit must be positive numbers');
      }

      const result = await this.withdrawalsService.getAllWithdrawals(pageNum, limitNum);
      return {
        success: true,
        message: 'All withdrawals retrieved successfully',
        data: result
      };
    } catch (error) {
      throw new BadRequestException({
        success: false,
        message: 'Failed to retrieve withdrawals',
        error: error.message
      });
    }
  }


  // Updated Controller
@Get('yearly')
@UseGuards(JwtVeterniaGuard)
async getYearlyWithdrawals(@Request() req, @Query('year') year?: string) {
  try {
    const userId = req.user['sub'];
    
    // If no year provided, default to current year
    console.log("year", year);
    console.log("userId", userId);
    const targetYear = year || new Date().getFullYear().toString();
    
    // Validate year format
    if (!/^\d{4}$/.test(targetYear)) {
      throw new BadRequestException('Year must be a 4-digit number');
    }
    
    const yearNum = parseInt(targetYear);
    const currentYear = new Date().getFullYear();
    
    // Optional: Add reasonable year range validation
    if (yearNum < 2000 || yearNum > currentYear + 1) {
      throw new BadRequestException(`Year must be between 2000 and ${currentYear + 1}`);
    }
    
    const result = await this.withdrawalsService.getYearlyWithdrawals(userId, targetYear);
    
    return {
      success: true,
      message: `Yearly withdrawals for ${targetYear} retrieved successfully`,
      data: result
    };
    
  } catch (error) {
    throw new BadRequestException({
      success: false,
      message: 'Failed to retrieve yearly withdrawals',
      error: error.message
    });
  }
}
}
