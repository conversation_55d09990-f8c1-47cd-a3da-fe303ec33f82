import {
    Controller,
    Post,
    UseGuards,
    UseInterceptors,
    UploadedFile,
    Body,
    Req,
    Get,
    Param,
    Patch,
    Delete,
    Query,
    Put,
  } from '@nestjs/common';
  import { FileInterceptor } from '@nestjs/platform-express';
  import { JwtAdminGuard } from '../../shared/guards/jwt-admin';
  import { Request } from 'express';
  import { CreateBannerDto } from './dto/create_banner.dto';
  import { AdminService } from './admin.service';
  import { Banner } from './schemas/bannerSchema';
  import { SignupDto } from '../auth/dto/signup.dto';
  
  @Controller('/api/v1/admin')
  export class AdminController {
    constructor(private readonly adminService: AdminService) {}
  
    @UseGuards(JwtAdminGuard)
    @UseInterceptors(FileInterceptor('file'))
    @Post('banner')
    async createBanner(
      @Body() dto: CreateBannerDto,
      @UploadedFile() file?: Express.Multer.File,
     // @Req() req: Request,
    ) {
      return this.adminService.createBanner(dto, file);
    }
  
    // @UseGuards(JwtAdminGuard)
    @Get('banners')
    async getAllBanners(): Promise<Banner[]> {
      return this.adminService.getAllBanners();
    }
  
    @UseGuards(JwtAdminGuard)
    @UseInterceptors(FileInterceptor('file'))
    @Patch('banner/:id')
    async editBanner(
      @Param('id') id: string,
      @Body() dto: CreateBannerDto,
      @UploadedFile() file?: Express.Multer.File,
    ) {
      return this.adminService.editBanner(id, dto, file);
    }
  
    @UseGuards(JwtAdminGuard)
    @Delete('banner/:id')
    async deleteBanner(@Param('id') id: string) {
      return this.adminService.deleteBanner(id);
    }
    @UseGuards(JwtAdminGuard)
    @Get('users')
    async getAllUsers(@Query() dto: { page?: number; limit?: number }) {
      return this.adminService.getAllUsers(dto);
    }
  
    @UseGuards(JwtAdminGuard)
    @Get('veterinarians')
    async getAllVeterinarians(@Query() dto: { page?: number; limit?: number }) {
      return this.adminService.getAllVeternia(dto);
    }
  
    @UseGuards(JwtAdminGuard)
    @Get('user/:id')
    async getUserById(@Param('id') id: string) {
      return this.adminService.getUserById(id);
    }
  
    // Toggle block/unblock user
    @UseGuards(JwtAdminGuard)
    @Put('block-toggle/:id')
    async blockUnblockUser(@Param('id') id: string) {
      return this.adminService.blockUnBlockUser(id);
    }
  
    // Toggle approve/disapprove veterinarian
    @UseGuards(JwtAdminGuard)
    @Patch('approve-toggle/:id')
    async approveDisapproveVet(@Param('id') id: string) {
      return this.adminService.approveDisapproveVet(id);
    }
  
    // Dashboard data
    @UseGuards(JwtAdminGuard)
    @Get('dashboard')
    async getDashboardData() {
      return this.adminService.getDashBoardData();
    }

    // ------------------ Admin Operations ----------------------------

    @UseGuards(JwtAdminGuard)
    @Get('get-all-admins')
    async getAllAdmin(@Query() dto: { page?: number; limit?: number }) {
      return this.adminService.getAllAdmin(dto);
    }

    @UseGuards(JwtAdminGuard)
    @Post('add-admin')
    async addAdmin(@Body() dto: any) {
      return this.adminService.addAdmin(dto);
    }
    @UseGuards(JwtAdminGuard)
    @Patch('edit-admin/:id')
    async editAdmin(@Param('id') id: string, @Body() dto: any) {
      return this.adminService.editAdmin(id, dto);
    }
    @UseGuards(JwtAdminGuard)
    @Delete('delete-admin/:id')
    async deleteAdmin(@Param('id') id: string) {
      return this.adminService.deleteAdmin(id);
    }

    @UseGuards(JwtAdminGuard)
    @Get('/:id')
    async getAdminById(@Param('id') id: string) {
      return this.adminService.getAdminById(id);
    }
  }