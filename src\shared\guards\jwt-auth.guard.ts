import {
    CanActivate,
    ExecutionContext,
    Injectable,
    UnauthorizedException,
  } from '@nestjs/common';
  import { Request } from 'express';
  import * as jwt from 'jsonwebtoken';
  import { ConfigService } from '@nestjs/config';
  
  @Injectable()
  export class JwtAuthGuard implements CanActivate {
    constructor(private readonly configService: ConfigService) {}
  
    canActivate(context: ExecutionContext): boolean {
      const request = context.switchToHttp().getRequest<Request>();
      const token = this.extractTokenFromHeader(request);
  
      if (!token) {
        throw new UnauthorizedException('Token not provided');
      }
  
      try {
        const secret = process.env.JWT_SECRET;
        const decoded = jwt.verify(token, secret);
        // Optionally attach user to request
        request.user = decoded;
        return true;
      } catch (err) {
        throw new UnauthorizedException('Invalid or expired token');
      }
    }
  
    private extractTokenFromHeader(request: Request): string | null {
      const authHeader = request.headers['authorization'];
      if (!authHeader) return null;
  
      const parts = authHeader.split(' ');
      if (parts.length !== 2 || parts[0] !== 'Bearer') return null;
  
      return parts[1];
    }
  }
  