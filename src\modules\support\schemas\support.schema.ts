import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document as MongooseDocument, Types } from "mongoose";

export type SupportDocument = MongooseDocument & Support;

@Schema({ timestamps: true })
export class Support {
    @Prop({ type: Types.ObjectId, ref: 'User', required: true })
    userId: Types.ObjectId;

    @Prop({ required: true })
    title: string;

    @Prop({ required: true })
    description: string;

    @Prop({ default: "" })
    image: string;
}

export const SupportSchema = SchemaFactory.createForClass(Support);
