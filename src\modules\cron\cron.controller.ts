import { Controller, Post, UseGuards } from '@nestjs/common';
import { CronService } from './cron.service';
import { JwtAuthGuard } from '../../shared/guards/jwt-auth.guard';

@Controller('cron')
export class CronController {
  constructor(private readonly cronService: CronService) {}

  /**
   * Manual trigger for booking completion check (for testing purposes)
   * This endpoint allows administrators to manually trigger the cron job
   */
  @UseGuards(JwtAuthGuard)
  @Post('trigger-booking-completion')
  async triggerBookingCompletion() {
    await this.cronService.triggerBookingCompletionCheck();
    return {
      success: true,
      message: 'Booking completion check triggered successfully'
    };
  }
}
