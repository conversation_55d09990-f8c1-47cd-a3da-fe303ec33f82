import { Modu<PERSON> } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { MongooseModule } from '@nestjs/mongoose';
import { CronService } from './cron.service';
import { CronController } from './cron.controller';
import { booking, bookingSchema } from '../booking/schemas/bookingSchemas';
import { VideoCall, VideoCallSchema } from '../videoCalling/schemas/video-call.schema';
import { User, UserSchema } from '../users/schemas/user.schema';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    MongooseModule.forFeature([
      { name: booking.name, schema: bookingSchema },
      { name: VideoCall.name, schema: VideoCallSchema },
      { name: User.name, schema: UserSchema },
    ]),
  ],
  controllers: [CronController],
  providers: [CronService],
  exports: [CronService],
})
export class CronModule {}
