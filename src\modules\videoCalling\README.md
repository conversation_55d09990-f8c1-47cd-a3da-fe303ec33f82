# Video Calling Module

This module provides Agora video calling functionality for the Elder Pet application.

## Features

- Create video call sessions
- Generate Agora tokens for secure access
- Join video calls
- End video calls with duration tracking
- Video call history
- Active call management

## Environment Variables

Add the following environment variables to your `.env` file:

```env
AGORA_APP_ID=your_agora_app_id_here
AGORA_APP_CERTIFICATE=your_agora_app_certificate_here
```

## API Endpoints

### POST /video-calling/create
Create a new video call session.

**Request Body:**
```json
{
  "channelName": "unique_channel_name",
  "userId": "user_id_optional",
  "veterinarianId": "vet_id_optional",
  "startTime": "2024-01-01T10:00:00Z"
}
```

### POST /video-calling/join
Join an existing video call.

**Request Body:**
```json
{
  "channelName": "channel_name",
  "userId": "user_id"
}
```

### PATCH /video-calling/end
End an active video call.

**Request Body:**
```json
{
  "channelName": "channel_name",
  "duration": 30
}
```

### GET /video-calling/history?userId=user_id
Get video call history for a user.

### GET /video-calling/active/:channelName
Get active video call information.

### GET /video-calling/token/:channelName/:userId
Generate a token for joining a video call.

## Database Schema

The module uses a MongoDB schema with the following fields:

- `appId`: Agora App ID
- `channelName`: Unique channel identifier
- `token`: Generated Agora token
- `userId`: User participating in the call
- `veterinarianId`: Veterinarian participating in the call
- `status`: Call status (scheduled, active, ended)
- `startTime`: Call start time
- `endTime`: Call end time
- `duration`: Call duration in minutes

## Setup Instructions

1. Install the required package: `npm install agora-token`
2. Add environment variables to your `.env` file
3. The module is automatically imported in the main app module
4. Start your NestJS application

## Usage Example

```typescript
// Create a video call
const videoCall = await videoCallingService.createVideoCall({
  channelName: 'consultation-123',
  userId: 'user123',
  veterinarianId: 'vet456'
});

// Join the call
const joinInfo = await videoCallingService.joinVideoCall({
  channelName: 'consultation-123',
  userId: 'user123'
});

// End the call
await videoCallingService.endVideoCall({
  channelName: 'consultation-123',
  duration: 25
});
```
