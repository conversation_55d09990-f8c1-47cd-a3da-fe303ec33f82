import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class RawBodyMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // Since we're using body-parser.raw() in main.ts,
    // req.body should already be a Buffer
    if (req.body) {
      req.rawBody = req.body;
    }
    next();
  }
}