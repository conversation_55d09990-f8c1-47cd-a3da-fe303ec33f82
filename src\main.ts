import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import * as cors from 'cors';
import * as bodyParser from 'body-parser';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Use CORS middleware globally
  app.use(cors());

  // CRITICAL: Apply webhook middleware BEFORE other body parsers
  // This ensures Stripe webhooks get raw body data
  app.use('/payments/webhook', bodyParser.raw({ 
    type: 'application/json',
    limit: '10mb'
  }));

  // Apply global validation pipe (this will use JSON parser for other routes)
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,            
    forbidNonWhitelisted: true, 
    transform: true, 
    transformOptions: {
      enableImplicitConversion: true,
    },           
  }));

  const port = process.env.PORT ?? 4000;
  await app.listen(port);
  console.log(`Application is running on port ${port}`);
}
bootstrap();