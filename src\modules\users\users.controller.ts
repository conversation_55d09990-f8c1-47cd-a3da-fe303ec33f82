import { Controller, UseGuards, Patch, Body, Req,UseInterceptors, UploadedFile,Get, Query,Put, Delete  } from '@nestjs/common';
import {JwtAuthGuard}  from '../../shared/guards/jwt-auth.guard';
import { UsersService } from './users.service';
import {JwtStrategy}  from '../../shared/guards/jwt.strategy';
import { Request } from 'express';
import { updateUserDto } from './dto/updateUser.dto';
import { updateVeterniaDto } from './dto/updateVeternia.dto';
import { UploadService } from '../upload/upload.service';
import { UploadedFiles } from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import {CompleteVeterniaProfileDto} from './dto/completeVeternia.dto'
import { BadRequestException } from '@nestjs/common';
import { getAvailableSlotDto } from './dto/getAvailableSlot.dto';
import { get } from 'mongoose';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { JwtVeterniaGuard } from 'src/shared/guards/jwt-veternia';
import { JwtUserGuard } from 'src/shared/guards/jwt-user';
import { Param } from '@nestjs/common';
import { FileValidationMiddleware } from '../middlewares/fileValidationmiddlewares';
@Controller('users')
export class UsersController {

    constructor(private readonly UsersService:UsersService){

    }
    @UseGuards(JwtAuthGuard)
    @UseInterceptors(FileInterceptor('file'))
  @Patch('edit-profile')
  async updateProfile(@Body() dto: updateUserDto, @Req() req: Request,@UploadedFile() file?: Express.Multer.File,) {
    const userId = req.user['sub'];
   
    return this.UsersService.updateProfile(userId, dto,file)
  }

@UseGuards(JwtVeterniaGuard)
@Patch('edit-veternia-profile')
@UseInterceptors(
  FileFieldsInterceptor([
    { name: 'profilePicUrl', maxCount: 1 },
    { name: 'liscenceImages', maxCount: 10 }
  ])
)
async updateVeterniaProfile(
  @Body() dto: updateVeterniaDto,
  @Req() req: Request,
  @Body('licenceImagesToDelete') licenceImagesToDelete?: string[],
  @UploadedFiles() files?: { [fieldname: string]: Express.Multer.File[] }
) {
  const userId = req.user['sub'];
  // console.log('Received files:', files);
  // console.log('Received DTO:', dto);
  
  return this.UsersService.updateVeterniaProfile(userId, dto, files,licenceImagesToDelete);
}

  @UseGuards(JwtAuthGuard)
  @Get('veternia')
  async getAllVeternia(@Query('page')page?:string,
  @Query('limit')limit?:string
){
  const pageNumber = parseInt(page, 10) || 1;
  const limitNumber = parseInt(limit, 10) || 10;
  return this.UsersService.getAllVeternia(pageNumber, limitNumber);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('delete-profile')
  async deleteProfile(@Req() req: Request) {
    const userId = req.user['sub'];
    return this.UsersService.deleteProfile(userId);
  }



@UseGuards(JwtVeterniaGuard) 
@Get('veternia-profile')
async getVeterniaProfile(@Req() req: Request) {
  const userId = req.user['sub'];
  return this.UsersService.getVeterniaProfile(userId);
}

@UseGuards(JwtAuthGuard)
@Get('veternia-profile-by-id/:id')
async getVeterniaProfileById(@Param('id') id: string) {
  return this.UsersService.getVeterniaProfileById(id);
}


@UseGuards(JwtVeterniaGuard)
@Put('complete-profile')
@UseInterceptors(
  FileFieldsInterceptor([
    { name: 'profilePicUrl', maxCount: 1 },
    { name: 'liscenceImages', maxCount: 10 }
  ])
)
async completeProfile(
  @Body() dto: updateVeterniaDto,
  @Req() req: Request,
  @UploadedFiles() files?: { [fieldname: string]: Express.Multer.File[] }
) {
  const userId = req.user['sub'];
  // console.log('Received files:', files);
  // console.log('Received DTO:', dto);
  
  return this.UsersService.completeVeterniaProfile(userId, dto, files);
}



//@UseInterceptors(FilesInterceptor('files')) 

// @Put('complete-profile')
// async completeVeterniaProfile(
//   @Body() dto: CompleteVeterniaProfileDto,
//   @Req() req: Request,
//   @UploadedFiles() files?: Express.Multer.File[],
//   @UploadedFile() file?: Express.Multer.File,) {
 

//   if (typeof dto.availability === 'string') {
//     try {
   
//       dto.availability = JSON.parse(dto.availability);
//       console.log('Parsed availability:', dto.availability);
//     } catch (error) {
     
//       throw new BadRequestException('Invalid JSON for availability');
//     }
//   }

//   const userId = req.user['sub'];
  

//   const result = await this.UsersService.completeVeterniaProfile(dto, userId, files);

//   console.log('Service result:', result);

//   return result;
// }

// @UseGuards(JwtAuthGuard)
// @Put('complete-profile-S')
// @UseInterceptors(
//   FileFieldsInterceptor([
//     { name: 'file', maxCount: 1 }, // Profile picture
//     { name: 'files', maxCount: 10 } // Licence images
//   ])
// )
// async completeProfile(
//   @Body() dto: CompleteVeterniaProfileDto,
//   @Req() req: Request,
//   @UploadedFiles() uploadedFiles: { file?: Express.Multer.File[]; files?: Express.Multer.File[] }
// ) {
//   if (typeof dto.availability === 'string') {
//     try {
//       dto.availability = JSON.parse(dto.availability);
//     } catch {
//       return {
//         statusCode: 400,
//         message: 'Invalid JSON for availability',
//         success: false,
//       };
//     }
//   }
// console.log("req.body",req.body);
//   const userId = req.user['sub'];

//   return await this.UsersService.completeProfile(
//     dto,
//     userId,
//     uploadedFiles.files || [],
//     uploadedFiles.file ? uploadedFiles.file[0] : null
//   );
// }

@UseGuards(JwtAuthGuard)
  @Get('available-slots')
  async getAllAvailableSlots(
    @Query() dto: getAvailableSlotDto,
    @Req() req: Request,
  ) {
    const userId = req.user['sub']; // Vet ID from JWT
    return this.UsersService.getAllAvailableSlots(dto);
  }

  @UseGuards(JwtUserGuard)
  @Get('free-trial-slots')
  async getFreeTrialSlots(@Req() req: Request) {
    const userId = req.user['sub'];
    return this.UsersService.getFreeTrialSlots(userId);
  }

  @UseGuards(JwtUserGuard)
  @Patch('reduce-free-trial-slots')
  async reduceFreeTrialSlots(@Req() req: Request) {
    const userId = req.user['sub'];
    return this.UsersService.reduceFreeTrialSlots(userId);
  }

}
