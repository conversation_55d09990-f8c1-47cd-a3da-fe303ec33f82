import { Modu<PERSON> } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { UsersModule } from '../users/users.module';

import { MailModule } from 'src/mail/mail.module';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { JwtStrategy } from './strategies/jwt.strategy';
//import { PaymentModule } from "../../payment/payment.module"
@Module({
  controllers: [AuthController],
  providers: [AuthService,JwtStrategy],
  imports: [UsersModule,MailModule,PassportModule,JwtModule.register({
    secret: process.env.JWT_SECRET,
    signOptions: { expiresIn: '7d' },
  }),],

})
export class AuthModule {}
