import { IsOptional, IsString, IsEmail, IsBoolean, IsIn } from 'class-validator';

export class updateUserDto {
  @IsOptional()
  @IsString()
  userName?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  password?: string;

  @IsOptional()
  @IsBoolean()
  isVerified?: boolean;

  @IsOptional()
  @IsString()
  profilePicUrl?: string;

  @IsOptional()
  @IsIn(["user", "veternia"])
  userType?: string;

  @IsOptional()
  @IsString()
  phoneNumber?: string;
}
