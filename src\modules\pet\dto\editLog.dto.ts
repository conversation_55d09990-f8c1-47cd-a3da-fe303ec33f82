import { IsO<PERSON>al, IsBoolean, IsMongoId,IsString, IsN<PERSON>ber, Min } from 'class-validator';

export class EditLogDto {
  @IsOptional()
  @IsMongoId({ message: 'Invalid petId format' })
  petId?: string;

  @IsOptional()
  @IsNumber({}, { message: 'Health must be a number' })
  // @Min(0, { message: 'Health must be at least 0' })
  health?: number;

  @IsOptional()
  @IsBoolean({ message: 'Eating must be a boolean value' })
  eating?: boolean;

  @IsOptional()
  @IsBoolean({ message: 'normalP must be a boolean value' })
  normalP?: boolean;

  @IsOptional()
  @IsBoolean({ message: 'sleepCycle must be a boolean value' })
  sleepCycle?: boolean;

  @IsOptional()
  @IsBoolean({ message: 'movedE must be a boolean value' })
  movedE?: boolean;
  @IsOptional()
  @IsString()
  behavious: string
  @IsOptional()
  @IsString()
  physicalSymptoms: string


  @IsOptional()
  @IsBoolean({ message: 'tookMed must be a boolean value' })
  tookMed?: boolean;


}
