// src/shared/guards/jwt-user.guard.ts
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { JwtAuthGuard } from './jwt-auth.guard';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtUserGuard extends JwtAuthGuard implements CanActivate {
  constructor(configService: ConfigService) {
    super(configService);
  }

  canActivate(context: ExecutionContext): boolean {
    // First verify the JWT token using parent guard
    const isValidToken = super.canActivate(context);
    
    if (!isValidToken) {
      return false;
    }

    // Get the request object
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // Check if user has user role
    if (!user || user.userType !== 'user') {
      throw new ForbiddenException('Access denied. User role required.');
    }
    if(user.blockStatus){
          throw new ForbiddenException('Access denied. Your account is blocked.');
    }

    return true;
  }
}