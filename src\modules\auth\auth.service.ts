import { Injectable,ConflictException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { SignupDto } from './dto/signup.dto';
import { LoginDto } from './dto/login.dto';
import { UsersService } from '../users/users.service';
import { generateOtp } from '../../shared/utils/generateOtp';
import { MailService } from '../../mail/mail.service';
import { Otp,OtpSchema } from '../users/schemas/otp.schema';
import * as bcrypt from "bcrypt";
import { verifyOtpDto } from './dto/verifyOtp.dto';
import { forgotPassDto } from './dto/forgotPass.dto';
import { UserDocument } from '../users/schemas/user.schema';
import {OAuth2Client} from 'google-auth-library';

//import { PaymentService } from '../../payment/payment.service';
@Injectable()
export class AuthService {
    constructor(
        private readonly userService: UsersService,
        //private readonly jwtService: JwtService,
        private readonly jwtService: JwtService,
        private readonly mailService:MailService,
       // private readonly paymentService:PaymentService,
    //     @Inject(forwardRef(() => paymentService))
    // private commonService: ,

      ) {}
    
    async signup(dto: SignupDto) {
        const existingUser = await this.userService.findByEmail(dto.email.toLowerCase());
        const otp = generateOtp();
        const expireAt = new Date();
        expireAt.setMinutes(expireAt.getMinutes() + 1);

        if(dto.userType === 'admin'){
          throw new ConflictException('Admin signup is not allowed.');
        }
   
        if (existingUser) {
          if (existingUser.isVerified) {
            throw new ConflictException("Email already exists");
          }
    
          await this.handleOtpResend((existingUser as any)._id.toString(), dto.email, otp, expireAt);
          
          return {
            message: "User already exists but not verified. OTP resent.",
            user: existingUser,
            password:dto.password
          };
        }
    
        const hashed = await bcrypt.hash(dto.password, 10);
        const newUser = await this.userService.create({ ...dto, password: hashed });
    
        await this.handleOtpResend(newUser._id.toString(), dto.email.toLowerCase(), otp, expireAt);
    
        return { message: "User created", user: newUser };
      }
      async verifyOtp(dto: verifyOtpDto) {
        const user = await this.userService.findById(dto.userId) as UserDocument;
        if (!user) {
          throw new ConflictException('User not found');
        }
        
        const isValid = await this.userService.verifyOtp(dto.userId, dto.otp);
        if (!isValid) {
          throw new ConflictException('Invalid or expired OTP');
        }

        const payload = {
          sub: user._id,
          email: user.email,
          userName: user.userName,
          userType:user.userType,
        };
    
        const accessToken = this.jwtService.sign(payload, {
          secret: process.env.JWT_SECRET,
          expiresIn: '30d',
        });
    
      
        return { message: 'OTP verified successfully',accessToken:accessToken };
      }

      async forgotPass(dto: forgotPassDto) {
        const findUser = await this.userService.findByEmail(dto.email);
      
        if (!findUser) {
          throw new ConflictException('User not found');
        }
      
        const otp = generateOtp();
        const expireAt = new Date(Date.now() + 2 * 60 * 1000);
      
        await this.handleOtpResend((findUser as any)._id.toString(), dto.email, otp, expireAt);
      
        return {
          message: 'OTP sent to email for password reset',
          user:findUser
        };
      }
      async resetPassword(dto: { userId: string;  newPassword: string }) {
        const user = await this.userService.findById(dto.userId);
        if (!user) {
          throw new ConflictException('User not registered');
        }
      
        const hashed = await bcrypt.hash(dto.newPassword, 10);
        await this.userService.updatePassword(dto.userId, hashed);
      
        return { message: 'Password reset successfully' };
      }
      async verifyForgotOtp(dto: verifyOtpDto) {
        const user = await this.userService.findById(dto.userId);
        if (!user) {
          throw new ConflictException('User not found');
        }
        console.log("dt",dto.userId,dto.otp)
        const isValid = await this.userService.verifyForgotOtp(dto.userId, dto.otp);
        console.log("isValid",isValid)
        if (!isValid) {
          throw new ConflictException('Invalid or expired OTP');
        }
      
        return { message: 'OTP verified successfully' };
      }

      async logIn(dto: LoginDto) {
        try {
          const user = await this.userService.findByEmail(dto.email.toLowerCase() as string);

          if(user.isGoogleUser){
            throw new ConflictException('Please login with google');
          }
      
          if (!user) {
            throw new ConflictException('User does not exist');
          }
      
          const match = await bcrypt.compare(dto.password as string, user.password as string);
      
          if (!match) {
            throw new ConflictException('Invalid credentials');
          }
      
          if (!user.isVerified) {
            throw new ConflictException(`Please verify your account before logging in. ${user.isVerified}`);
          }
          if (dto.userType && dto.userType !== user.userType) {
            throw new ConflictException(`Invalid user type. Expected: ${user.userType}`);
          }

          const payload = {
            sub: user._id,
            email: user.email,
            userName: user.userName,
            userType:user.userType
          };
      
          const accessToken = this.jwtService.sign(payload, {
            secret: process.env.JWT_SECRET,
            expiresIn: '30d',
          });
          if(user.userType === 'veternia' && !user.isComplete){
              return {
            message: 'Please complete your profile',
            accessToken,
            user: {
              id: user._id,
              email: user.email,
              userName: user.userName,
              isVerified:user.isVerified,
              profilePicUrl:user.profilePicUrl,
              userType:user.userType,
              isApprove:user.approve,
              blockStatus:user.blockStatus,
              isCompleted:user.isComplete,
              phoneNumber:user.phoneNumber,
              isCompletedPaymentSetup:user.isCompletedPaymentSetup
            },
            };
          }

          if(user.userType === 'veternia' && !user.isCompletedPaymentSetup){
            return {
            message: 'Please complete your payment setup',
            accessToken,
            user: {
              id: user._id,
              email: user.email,
              userName: user.userName,
              isVerified:user.isVerified,
              profilePicUrl:user.profilePicUrl,
              userType:user.userType,
              isApprove:user.approve,
              blockStatus:user.blockStatus,
              isCompleted:user.isComplete,
              phoneNumber:user.phoneNumber,
              isCompletedPaymentSetup:user.isCompletedPaymentSetup
            },
            };
          }
          if(user.userType === 'veternia' && !user.approve){
            throw new ConflictException(`Your account is not approved yet. Please wait.`);
          }
          if(user.blockStatus){
            throw new ConflictException(`Your account is blocked. Please contact admin.`);
          }
          return {
            message: 'Login successful',
            accessToken,
            user: {
              id: user._id,
              email: user.email,
              userName: user.userName,
              isVerified:user.isVerified,
              profilePicUrl:user.profilePicUrl,
              userType:user.userType,
              isApprove:user.approve,
              blockStatus:user.blockStatus,
              isCompleted:user.isComplete,
              phoneNumber:user.phoneNumber,
              isCompletedPaymentSetup:user.isCompletedPaymentSetup,
              connected_acc_id:user.connected_acc_id,
              connected_external_acc_id:user.connected_external_acc_id,
              payouts_enabled:user.payouts_enabled
            },
          };
        } catch (error) {
          throw new ConflictException(error.message || 'Something went wrong during login');
        }
      }

      async resendOTP(email: string) {
        try {
        const exixtingUser = await this.userService.findByEmail(email.toLowerCase());
        if (!exixtingUser) {
          throw new ConflictException('User not found');
        }
        const otp = generateOtp();

        const expireAt = new Date();
        expireAt.setMinutes(expireAt.getMinutes() + 1);
        console.log("expireAt",expireAt);
        await this.handleOtpResend((exixtingUser as any)._id.toString(), email, otp, expireAt);
        return { message: 'OTP resent successfully' };
      }
      catch (error) {
        throw new ConflictException(error.message || 'Something went wrong during OTP resend please try again later.');
      }
    }

async googleLogin(token: string, userType: string) {
  try {
    const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);
    
    if (!token) {
      return {
        success: false,
        message: 'Google token is required'
      };
    }

    // Check if token is an access token (starts with ya29.) or ID token (JWT format)
    let payload;
    
    if (token.startsWith('ya29.')) {
      // This is an access token, we need to get user info from Google API
      try {
        const response = await fetch(`https://www.googleapis.com/oauth2/v2/userinfo?access_token=${token}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch user info from Google');
        }
        
        const userInfo = await response.json();
        payload = {
          email: userInfo.email,
          name: userInfo.name,
          picture: userInfo.picture,
          sub: userInfo.id
        };
      } catch (error) {
        console.error('Error fetching user info:', error);
        return {
          success: false,
          message: 'Failed to verify Google token'
        };
      }
    } else {
      // This should be an ID token (JWT format)
      try {
        const ticket = await client.verifyIdToken({
          idToken: token,
          audience: process.env.GOOGLE_CLIENT_ID
        });
        payload = ticket.getPayload();
      } catch (error) {
        console.error('Error verifying ID token:', error);
        return {
          success: false,
          message: 'Invalid Google ID token'
        };
      }
    }

    const { email, name, picture, sub: googleId } = payload;

    if (!email) {
      return {
        success: false,
        message: 'Email not found in Google token'
      };
    }

    // Find or create user
    let user = await this.userService.findByEmail(email as string);

    if (!user) {
      // Create new user if doesn't exist
      user = await this.userService.create({
        userName: name || 'Google User',
        email: email.toLowerCase(),
        googleId: googleId,
        profilePicUrl: picture,
        password: "googleLogin",
        confirmPass: "googleLogin",
        isVerified: true,
        userType: userType,
        phoneNumber: "",
        isGoogleUser: true,
      });
      console.log('New Google user created:', email);
    } else {
      // Update existing user with Google info
      user.googleId = googleId;
      user.profilePicUrl = picture;
      user.isGoogleUser = true;
      user.isVerified = true;
      await user.save();
      console.log('Existing user updated with Google info:', email);
    }

    // Remove duplicate save() call
    console.log('user saved:', user);

    const jwtPayload = {
      sub: user._id,
      email: user.email,
      userName: user.userName,
      userType: user.userType,
    };

    // Generate JWT token
    const accessToken = this.jwtService.sign(jwtPayload, {
      secret: process.env.JWT_SECRET,
      expiresIn: '30d',
    });

    console.log('Google user logged in:', user.email);

    return {
      success: true,
      user: {
        id: user._id,
        fullname: user.userName,
        email: user.email,
        isVerified: user.isVerified,
        role: user.userType,
        googleId: user.googleId,
        profilePicUrl: user.profilePicUrl,
        isApprove:user.approve,
        blockStatus:user.blockStatus,
        isCompleted:user.isComplete,
        phoneNumber:user.phoneNumber,
        isCompletedPaymentSetup:user.isCompletedPaymentSetup,
        connected_acc_id:user.connected_acc_id,
        connected_external_acc_id:user.connected_external_acc_id,
        payouts_enabled:user.payouts_enabled        
      },
      accessToken: accessToken
    };

  } catch (err) {
    console.error('Google Login Error:', err);
    return {
      success: false,
      message: 'Google authentication failed'
    };
  }
}
      
    async getStripeAccountCompletionStatus(email: string) {
    const user = await this.userService.findByEmail(email.toLowerCase())
    if (!user) {
      return {
        success: false,
        isCompletedPaymentSetup: false,
        message: 'User not found',
      };
    }

    if(user.connected_acc_id !== 'none' && user.connected_external_acc_id !== 'none' && user.payouts_enabled === true){
        user.isCompletedPaymentSetup = true;
        user.isComplete = true;
        await user.save();
        const otp = generateOtp();
        const expireAt = new Date();
        expireAt.setMinutes(expireAt.getMinutes() + 1);
        await this.handleOtpResend(user._id.toString(), user.email.toLowerCase(), otp, expireAt);
    }

    return {
      isCompletedPaymentSetup: user.isCompletedPaymentSetup,
    };
  }
      
        async handleOtpResend(userId: string, email: string, otp: string, expireAt: Date) {
        await this.userService.deleteOtps(userId);
        await this.userService.createOtp(userId, otp, expireAt);
        await this.mailService.sendOtpEmail(email, otp);
      }
      
      
}


