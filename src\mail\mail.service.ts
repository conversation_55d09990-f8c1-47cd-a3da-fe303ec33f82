import { Injectable } from '@nestjs/common';
import * as nodemailer from "nodemailer";
import { Subject } from 'rxjs';
import { text } from 'stream/consumers';
@Injectable()
export class MailService {
    private transporter=nodemailer.createTransport({
        service:"Gmail",
        auth:{
            user:process.env.AdminEmail,
            pass:process.env.AdminPassword
        }
    })
    async sendOtpEmail(to:string,otp:string){
        const mailOption={
            from: process.env.AdminEmail,
            to,
            Subject:"OTP FOR VERIFICATION",
            text:`Your OTP is : ${otp}`
        };
        await this.transporter.sendMail(mailOption);
    }
}

/*/
AdminEmail=<EMAIL>
AdminPassword=jjqi ceks efgo pslj
/*/