# Cron Job Module

This module provides automated scheduled tasks for the Elder Pet application, specifically for handling booking completion and veterinarian earnings updates.

## Features

- **Automated Booking Completion**: Checks ended bookings and updates their status based on video call participation
- **Veterinarian Earnings Update**: Automatically updates veterinarian earnings when bookings are completed
- **Manual Trigger**: Provides API endpoint for manual testing and triggering of cron jobs

## How It Works

### Booking Completion Flow

1. **Cron Job Execution**: Runs every 5 minutes to check for ended bookings
2. **Booking Identification**: Finds all active bookings where `endTime < current time`
3. **Video Call Verification**: For each ended booking:
   - Checks if booking has a `channelName`
   - Finds corresponding video call record using `channelName`
   - Verifies if both `isUserJoinedCall` and `isVeterniaJoinedCall` are `true`
4. **Status Update**:
   - If both parties joined: Sets `bookingStatus` to `"completed"`
   - If either party didn't join or no video call record exists: Sets `bookingStatus` to `"incomplete"`

### Earnings Update Flow

When a booking is marked as `"completed"`:
1. Retrieves the `amountCharged` from the booking
2. Updates the veterinarian's user record:
   - Adds `amountCharged` to `total_earnings`
   - Adds `amountCharged` to `allowed_withdrawl_balance`

## Configuration

### Cron Schedule
- **Frequency**: Every 5 minutes
- **Cron Expression**: `CronExpression.EVERY_5_MINUTES`
- **Can be modified** in `src/modules/cron/cron.service.ts`

### Database Models Used
- **Booking Model**: `src/modules/booking/schemas/bookingSchemas.ts`
- **VideoCall Model**: `src/modules/videoCalling/schemas/video-call.schema.ts`
- **User Model**: `src/modules/users/schemas/user.schema.ts`

## API Endpoints

### Manual Trigger
```
POST /cron/trigger-booking-completion
```
**Authentication**: Requires JWT token
**Description**: Manually triggers the booking completion check
**Response**:
```json
{
  "success": true,
  "message": "Booking completion check triggered successfully"
}
```

## Installation & Setup

The cron module is automatically imported in the main `AppModule`. No additional setup is required.

### Dependencies
- `@nestjs/schedule`: For cron job functionality
- `@nestjs/mongoose`: For database operations

## Testing

Run the cron module tests:
```bash
npm test -- src/modules/cron
```

### Test Coverage
- ✅ Booking completion with both parties joined
- ✅ Booking completion with user not joined
- ✅ Booking completion with veterinarian not joined
- ✅ Booking completion with no video call record
- ✅ Booking completion with no channel name
- ✅ Manual trigger functionality
- ✅ Error handling

## Logging

The cron service includes comprehensive logging:
- Info logs for normal operations
- Warning logs for edge cases
- Error logs for failures

Logs can be found in the application console with the prefix `[CronService]`.

## Error Handling

The cron job includes robust error handling:
- Individual booking processing errors don't stop the entire job
- Database connection issues are logged and handled gracefully
- Missing data scenarios are handled appropriately

## Monitoring

To monitor the cron job:
1. Check application logs for `[CronService]` entries
2. Use the manual trigger endpoint for testing
3. Monitor database changes in booking status and user earnings

## Future Enhancements

Potential improvements:
- Add metrics collection for cron job performance
- Implement retry logic for failed operations
- Add email notifications for failed bookings
- Create dashboard for cron job monitoring
