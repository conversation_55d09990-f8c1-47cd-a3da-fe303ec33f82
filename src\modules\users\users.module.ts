import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import {User,UserSchema} from "../users/schemas/user.schema"
import { Otp,OtpSchema } from './schemas/otp.schema';
import { UploadModule } from '../upload/upload.module';
import { bookingSchema,booking } from '../booking/schemas/bookingSchemas';
import { PaymentModule } from '../../payment/payment.module';
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: Otp.name, schema: OtpSchema },
      { name: booking.name, schema: bookingSchema }
    ]),
    UploadModule,PaymentModule
  ],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
