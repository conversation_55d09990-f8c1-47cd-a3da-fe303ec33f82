import { 
    Controller, 
    Post, 
    Get, 
    Put, 
    Delete, 
    Body, 
    Param, 
    Query, 
    UseGuards, 
    Req, 
    UseInterceptors, 
    UploadedFile 
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Request } from 'express';
import { SupportService } from './support.service';
import { CreateSupportDto } from './dto/create-support.dto';
import { UpdateSupportDto } from './dto/update-support.dto';
import { JwtAuthGuard } from '../../shared/guards/jwt-auth.guard';

@Controller('support')
export class SupportController {
    constructor(private readonly supportService: SupportService) {}

    @UseGuards(JwtAuthGuard)
    @UseInterceptors(FileInterceptor('file'))
    @Post()
    async createSupport(
        @Body() dto: CreateSupportDto,
        @Req() req: Request,
        @UploadedFile() file?: Express.Multer.File,
    ) {
        const userId = req.user['sub'];
        return this.supportService.createSupport(dto, userId, file);
    }

    @UseGuards(JwtAuthGuard)
    @Get('all')
    async getAllSupports(
        @Query('page') page?: string,
        @Query('limit') limit?: string,
    ) {
        const pageNumber = parseInt(page, 10) || 1;
        const limitNumber = parseInt(limit, 10) || 10;
        return this.supportService.getAllSupports(pageNumber, limitNumber);
    }

    @UseGuards(JwtAuthGuard)
    @Get('my-supports')
    async getUserSupports(
        @Req() req: Request,
        @Query('page') page?: string,
        @Query('limit') limit?: string,
    ) {
        const userId = req.user['sub'];
        const pageNumber = parseInt(page, 10) || 1;
        const limitNumber = parseInt(limit, 10) || 10;
        return this.supportService.getUserSupports(userId, pageNumber, limitNumber);
    }

    @UseGuards(JwtAuthGuard)
    @Get(':id')
    async getSupportById(@Param('id') supportId: string) {
        return this.supportService.getSupportById(supportId);
    }

    @UseGuards(JwtAuthGuard)
    @UseInterceptors(FileInterceptor('file'))
    @Put(':id')
    async updateSupport(
        @Param('id') supportId: string,
        @Body() dto: UpdateSupportDto,
        @Req() req: Request,
        @UploadedFile() file?: Express.Multer.File,
    ) {
        const userId = req.user['sub'];
        return this.supportService.updateSupport(supportId, dto, userId, file);
    }

    @UseGuards(JwtAuthGuard)
    @Delete(':id')
    async deleteSupport(
        @Param('id') supportId: string,
        @Req() req: Request,
    ) {
        const userId = req.user['sub'];
        return this.supportService.deleteSupport(supportId, userId);
    }
}
