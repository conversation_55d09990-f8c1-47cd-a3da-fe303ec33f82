import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { VideoCallingService } from './video-calling.service';
import { VideoCall } from './schemas/video-call.schema';
import { BadRequestException, NotFoundException } from '@nestjs/common';

describe('VideoCallingService', () => {
  let service: VideoCallingService;
  let mockVideoCallModel: any;

  const mockVideoCall = {
    _id: 'mockId',
    appId: 'testAppId',
    channelName: 'test-channel',
    token: 'test-token',
    userId: 'user123',
    veterinarianId: 'vet456',
    status: 'scheduled',
    startTime: new Date(),
    save: jest.fn().mockResolvedValue(this)
  };

  beforeEach(async () => {
    mockVideoCallModel = {
      findOne: jest.fn(),
      updateOne: jest.fn(),
      find: jest.fn(),
      constructor: jest.fn().mockImplementation(() => mockVideoCall)
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VideoCallingService,
        {
          provide: getModelToken(VideoCall.name),
          useValue: mockVideoCallModel
        }
      ]
    }).compile();

    service = module.get<VideoCallingService>(VideoCallingService);
    
    // Mock environment variables
    process.env.AGORA_APP_ID = 'test-app-id';
    process.env.AGORA_APP_CERTIFICATE = 'test-certificate';
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createVideoCall', () => {
    it('should create a video call successfully', async () => {
      mockVideoCallModel.findOne.mockResolvedValue(null);
      mockVideoCall.save.mockResolvedValue(mockVideoCall);

      const createDto = {
        channelName: 'test-channel',
        userId: 'user123',
        veterinarianId: 'vet456'
      };

      const result = await service.createVideoCall(createDto);
      
      expect(mockVideoCallModel.findOne).toHaveBeenCalledWith({
        channelName: 'test-channel',
        status: { $in: ['active', 'scheduled'] }
      });
      expect(result).toEqual(mockVideoCall);
    });

    it('should throw BadRequestException if channel already exists', async () => {
      mockVideoCallModel.findOne.mockResolvedValue(mockVideoCall);

      const createDto = {
        channelName: 'test-channel',
        userId: 'user123'
      };

      await expect(service.createVideoCall(createDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('joinVideoCall', () => {
    it('should join video call successfully', async () => {
      mockVideoCallModel.findOne.mockResolvedValue(mockVideoCall);
      mockVideoCallModel.updateOne.mockResolvedValue({ acknowledged: true });

      const joinDto = {
        channelName: 'test-channel',
        userId: 'user123'
      };

      const result = await service.joinVideoCall(joinDto, 'user123');
      
      expect(result).toHaveProperty('token');
      expect(result).toHaveProperty('appId');
      expect(result).toHaveProperty('channelName');
      expect(result.channelName).toBe('test-channel');
    });

    it('should throw NotFoundException if video call not found', async () => {
      mockVideoCallModel.findOne.mockResolvedValue(null);

      const joinDto = {
        channelName: 'non-existent-channel',
        userId: 'user123'
      };

      await expect(service.joinVideoCall(joinDto, 'user123')).rejects.toThrow(NotFoundException);
    });
  });

  describe('endVideoCall', () => {
    it('should end video call successfully', async () => {
      const activeCall = { ...mockVideoCall, status: 'active', startTime: new Date() };
      mockVideoCallModel.findOne.mockResolvedValue(activeCall);
      mockVideoCallModel.updateOne.mockResolvedValue({ acknowledged: true });

      const endDto = {
        channelName: 'test-channel',
        duration: 30
      };

      const result = await service.endVideoCall(endDto);
      
      expect(result).toEqual({ message: 'Video call ended successfully' });
      expect(mockVideoCallModel.updateOne).toHaveBeenCalled();
    });

    it('should throw NotFoundException if active call not found', async () => {
      mockVideoCallModel.findOne.mockResolvedValue(null);

      const endDto = {
        channelName: 'non-existent-channel'
      };

      await expect(service.endVideoCall(endDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getVideoCallHistory', () => {
    it('should return video call history', async () => {
      const mockHistory = [mockVideoCall];
      mockVideoCallModel.find.mockReturnValue({
        sort: jest.fn().mockResolvedValue(mockHistory)
      });

      const result = await service.getVideoCallHistory('user123');
      
      expect(result).toEqual(mockHistory);
      expect(mockVideoCallModel.find).toHaveBeenCalledWith({
        $or: [{ userId: 'user123' }, { veterinarianId: 'user123' }]
      });
    });
  });

  describe('getActiveVideoCall', () => {
    it('should return active video call', async () => {
      const activeCall = { ...mockVideoCall, status: 'active' };
      mockVideoCallModel.findOne.mockResolvedValue(activeCall);

      const result = await service.getActiveVideoCall('test-channel');
      
      expect(result).toEqual(activeCall);
      expect(mockVideoCallModel.findOne).toHaveBeenCalledWith({
        channelName: 'test-channel',
        status: 'active'
      });
    });
  });
});
