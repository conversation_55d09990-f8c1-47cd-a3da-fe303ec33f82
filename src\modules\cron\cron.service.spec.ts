import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { CronService } from './cron.service';
import { booking } from '../booking/schemas/bookingSchemas';
import { VideoCall } from '../videoCalling/schemas/video-call.schema';
import { User } from '../users/schemas/user.schema';

describe('CronService', () => {
  let service: CronService;
  let mockBookingModel: any;
  let mockVideoCallModel: any;
  let mockUserModel: any;

  const mockBooking = {
    _id: 'booking123',
    channelName: 'test-channel',
    amountCharged: 100,
    veterniaId: 'vet123',
    endTime: new Date(Date.now() - 10000), // 10 seconds ago
    bookingStatus: 'active'
  };

  const mockVideoCall = {
    channelName: 'test-channel',
    isUserJoinedCall: true,
    isVeterniaJoinedCall: true
  };

  const mockUser = {
    _id: 'vet123',
    total_earnings: 500,
    allowed_withdrawl_balance: 300
  };

  beforeEach(async () => {
    mockBookingModel = {
      find: jest.fn(),
      findByIdAndUpdate: jest.fn(),
    };

    mockVideoCallModel = {
      findOne: jest.fn(),
    };

    mockUserModel = {
      findByIdAndUpdate: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CronService,
        {
          provide: getModelToken(booking.name),
          useValue: mockBookingModel,
        },
        {
          provide: getModelToken(VideoCall.name),
          useValue: mockVideoCallModel,
        },
        {
          provide: getModelToken(User.name),
          useValue: mockUserModel,
        },
      ],
    }).compile();

    service = module.get<CronService>(CronService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('handleBookingCompletion', () => {
    it('should mark booking as completed when both parties joined', async () => {
      // Mock ended bookings
      mockBookingModel.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue([mockBooking])
      });

      // Mock video call with both parties joined
      mockVideoCallModel.findOne.mockResolvedValue(mockVideoCall);

      // Mock booking update
      mockBookingModel.findByIdAndUpdate.mockResolvedValue(mockBooking);

      // Mock user update for earnings
      mockUserModel.findByIdAndUpdate.mockResolvedValue({
        ...mockUser,
        total_earnings: 600,
        allowed_withdrawl_balance: 400
      });

      await service.handleBookingCompletion();

      expect(mockBookingModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'booking123',
        { bookingStatus: 'completed' }
      );

      expect(mockUserModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'vet123',
        {
          $inc: {
            total_earnings: 100,
            allowed_withdrawl_balance: 100
          }
        },
        { new: true }
      );
    });

    it('should mark booking as incomplete when user did not join', async () => {
      const incompleteVideoCall = {
        ...mockVideoCall,
        isUserJoinedCall: false
      };

      mockBookingModel.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue([mockBooking])
      });

      mockVideoCallModel.findOne.mockResolvedValue(incompleteVideoCall);
      mockBookingModel.findByIdAndUpdate.mockResolvedValue(mockBooking);

      await service.handleBookingCompletion();

      expect(mockBookingModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'booking123',
        { bookingStatus: 'incomplete' }
      );

      // Should not update earnings for incomplete booking
      expect(mockUserModel.findByIdAndUpdate).not.toHaveBeenCalled();
    });

    it('should mark booking as incomplete when veterinarian did not join', async () => {
      const incompleteVideoCall = {
        ...mockVideoCall,
        isVeterniaJoinedCall: false
      };

      mockBookingModel.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue([mockBooking])
      });

      mockVideoCallModel.findOne.mockResolvedValue(incompleteVideoCall);
      mockBookingModel.findByIdAndUpdate.mockResolvedValue(mockBooking);

      await service.handleBookingCompletion();

      expect(mockBookingModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'booking123',
        { bookingStatus: 'incomplete' }
      );

      expect(mockUserModel.findByIdAndUpdate).not.toHaveBeenCalled();
    });

    it('should mark booking as incomplete when no video call record exists', async () => {
      mockBookingModel.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue([mockBooking])
      });

      mockVideoCallModel.findOne.mockResolvedValue(null);
      mockBookingModel.findByIdAndUpdate.mockResolvedValue(mockBooking);

      await service.handleBookingCompletion();

      expect(mockBookingModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'booking123',
        { bookingStatus: 'incomplete' }
      );

      expect(mockUserModel.findByIdAndUpdate).not.toHaveBeenCalled();
    });

    it('should mark booking as incomplete when no channelName exists', async () => {
      const bookingWithoutChannel = {
        ...mockBooking,
        channelName: null
      };

      mockBookingModel.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue([bookingWithoutChannel])
      });

      mockBookingModel.findByIdAndUpdate.mockResolvedValue(bookingWithoutChannel);

      await service.handleBookingCompletion();

      expect(mockBookingModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'booking123',
        { bookingStatus: 'incomplete' }
      );

      expect(mockVideoCallModel.findOne).not.toHaveBeenCalled();
      expect(mockUserModel.findByIdAndUpdate).not.toHaveBeenCalled();
    });
  });

  describe('triggerBookingCompletionCheck', () => {
    it('should call handleBookingCompletion', async () => {
      const handleBookingCompletionSpy = jest.spyOn(service, 'handleBookingCompletion').mockResolvedValue();

      await service.triggerBookingCompletionCheck();

      expect(handleBookingCompletionSpy).toHaveBeenCalled();
    });
  });
});
