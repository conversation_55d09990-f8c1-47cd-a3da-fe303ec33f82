import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model , Types } from 'mongoose';
import { VideoCall, VideoCallDocument } from './schemas/video-call.schema';
import { CreateVideoCallDto } from './dto/create-video-call.dto';
import { JoinVideoCallDto } from './dto/join-video-call.dto';
import { EndVideoCallDto } from './dto/end-video-call.dto';
import { RtcTokenBuilder, RtcRole } from 'agora-token';
import { booking, bookingDocument } from 'src/modules/booking/schemas/bookingSchemas';

@Injectable()
export class VideoCallingService {
    private readonly appId: string;
    private readonly appCertificate: string;

    constructor(
        @InjectModel(VideoCall.name) private videoCallModel: Model<VideoCallDocument>,
        @InjectModel(booking.name) private bookingModel: Model<bookingDocument>,
    ) {
        // These should be set in environment variables
        this.appId = process.env.AGORA_APP_ID || '';
        this.appCertificate = process.env.AGORA_APP_CERTIFICATE || '';
    }

    async createVideoCall(createVideoCallDto: CreateVideoCallDto): Promise<VideoCall> {
        try {
            const { channelName, userId, veterinarianId, startTime } = createVideoCallDto;

            // Check if a video call with the same channel name already exists and is active
            const existingCall = await this.videoCallModel.findOne({
                channelName,
                status: { $in: ['active', 'scheduled'] }
            });
            

            if (existingCall) {
                throw new BadRequestException('A video call with this channel name is already active or scheduled');
            }

            // Generate Agora token
            const token = this.generateAgoraToken(channelName, userId || '0');

            const videoCall = new this.videoCallModel({
                appId: this.appId,
                channelName,
                token,
                userId,
                veterinarianId,
                startTime: startTime || new Date(),
                status: 'scheduled'
            });

            const savedCall = await videoCall.save();
            return savedCall;
        } catch (error) {
            if (error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException('Failed to create video call');
        }
    }

    async joinVideoCall(joinVideoCallDto: JoinVideoCallDto, userId:string): Promise<{ token: string; appId: string; channelName: string }> {
        try {
            const { channelName } = joinVideoCallDto;   
            

            const videoCall = await this.videoCallModel.findOne({
                channelName,
                status: { $in: ['scheduled', 'active'] }
            });

            if (!videoCall) {
                throw new NotFoundException('Video call not found or already ended');
            }

            if(videoCall.userId === userId || videoCall.veterinarianId === userId){
                console.log("User is allowed to join this call");
                    // Generate a new token for the user joining
                const token = this.generateAgoraToken(channelName, userId);
                if(videoCall.userId === userId){
                    videoCall.isUserJoinedCall = true;
                    await videoCall.save();
                }
                if(videoCall.veterinarianId === userId){
                    videoCall.isVeterniaJoinedCall = true;
                    await videoCall.save();
                }

                // Update the video call status to active if it was scheduled
                if (videoCall.status === 'scheduled') {
                    await this.videoCallModel.updateOne(
                        { _id: videoCall._id },
                        { 
                            status: 'active',
                            startTime: new Date()
                        }
                    );
                }

                return {
                    token,
                    appId: this.appId,
                    channelName
                };
            }
            else {  
                throw new NotFoundException('You are not allowed to join this call');
            }

            
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            throw new BadRequestException('Failed to join video call');
        }
    }

    async endVideoCall(endVideoCallDto: EndVideoCallDto): Promise<{ message: string }> {
        try {
            const { channelName, duration } = endVideoCallDto;

            const videoCall = await this.videoCallModel.findOne({
                channelName,
                status: 'active'
            });

            if (!videoCall) {
                throw new NotFoundException('Active video call not found');
            }

            const endTime = new Date();
            const calculatedDuration = duration || Math.floor((endTime.getTime() - videoCall.startTime.getTime()) / (1000 * 60));

            await this.videoCallModel.updateOne(
                { _id: videoCall._id },
                {
                    status: 'ended',
                    endTime,
                    duration: calculatedDuration
                }
            );

            return { message: 'Video call ended successfully' };
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            throw new BadRequestException('Failed to end video call');
        }
    }

    async getVideoCallHistory(userId?: string): Promise<VideoCall[]> {
        try {
            const query = userId 
                ? { $or: [{ userId }, { veterinarianId: userId }] }
                : {};

            return await this.videoCallModel.find(query).sort({ createdAt: -1 });
        } catch (error) {
            throw new BadRequestException('Failed to fetch video call history');
        }
    }

    async getActiveVideoCall(channelName: string): Promise<VideoCall | null> {
        try {
            return await this.videoCallModel.findOne({
                channelName,
                status: 'active'
            });
        } catch (error) {
            throw new BadRequestException('Failed to fetch active video call');
        }
    }

    private generateAgoraToken(channelName: string, uid: string): string {
        if (!this.appId || !this.appCertificate) {
            throw new BadRequestException('Agora credentials not configured');
        }

        const role = RtcRole.PUBLISHER;
        const expirationTimeInSeconds = 30*24*60*60;    // 30 days token 
        const currentTimestamp = Math.floor(Date.now() / 1000);
        const tokenExpire = currentTimestamp + expirationTimeInSeconds;
        console.log("tokenExpire",tokenExpire);
        const privilegeExpire = currentTimestamp + expirationTimeInSeconds;
        console.log("privilegeExpire",privilegeExpire);
        console.log("before uid",uid);

        return RtcTokenBuilder.buildTokenWithUserAccount(
        this.appId,
        this.appCertificate,
        channelName,
        uid, // string userId
        role,
        tokenExpire,
        privilegeExpire 
        );
    }
}