import { Test, TestingModule } from '@nestjs/testing';
import { SupportController } from './support.controller';
import { SupportService } from './support.service';
import { CreateSupportDto } from './dto/create-support.dto';
import { UpdateSupportDto } from './dto/update-support.dto';
import { JwtAuthGuard } from '../../shared/guards/jwt-auth.guard';
import { Types } from 'mongoose';

describe('SupportController', () => {
    let controller: SupportController;
    let service: SupportService;

    const mockSupportService = {
        createSupport: jest.fn(),
        getAllSupports: jest.fn(),
        getUserSupports: jest.fn(),
        getSupportById: jest.fn(),
        updateSupport: jest.fn(),
        deleteSupport: jest.fn(),
    };

    const mockRequest = {
        user: { sub: new Types.ObjectId().toString() },
    } as any;

    const mockSupport = {
        _id: new Types.ObjectId(),
        userId: new Types.ObjectId(),
        title: 'Test Support',
        description: 'Test Description',
        image: 'test-image.jpg',
        createdAt: new Date(),
        updatedAt: new Date(),
    };

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            controllers: [SupportController],
            providers: [
                {
                    provide: SupportService,
                    useValue: mockSupportService,
                },
            ],
        })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: jest.fn(() => true) })
        .compile();

        controller = module.get<SupportController>(SupportController);
        service = module.get<SupportService>(SupportService);
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
    });

    describe('createSupport', () => {
        it('should create a support request', async () => {
            const createSupportDto: CreateSupportDto = {
                title: 'Test Support',
                description: 'Test Description',
            };

            mockSupportService.createSupport.mockResolvedValue(mockSupport);

            const result = await controller.createSupport(createSupportDto, mockRequest);

            expect(service.createSupport).toHaveBeenCalledWith(
                createSupportDto,
                mockRequest.user.sub,
                undefined,
            );
            expect(result).toEqual(mockSupport);
        });
    });

    describe('getAllSupports', () => {
        it('should return all support requests with pagination', async () => {
            const mockResponse = {
                supports: [mockSupport],
                total: 1,
                page: 1,
                totalPages: 1,
            };

            mockSupportService.getAllSupports.mockResolvedValue(mockResponse);

            const result = await controller.getAllSupports('1', '10');

            expect(service.getAllSupports).toHaveBeenCalledWith(1, 10);
            expect(result).toEqual(mockResponse);
        });

        it('should use default pagination values', async () => {
            const mockResponse = {
                supports: [mockSupport],
                total: 1,
                page: 1,
                totalPages: 1,
            };

            mockSupportService.getAllSupports.mockResolvedValue(mockResponse);

            const result = await controller.getAllSupports();

            expect(service.getAllSupports).toHaveBeenCalledWith(1, 10);
            expect(result).toEqual(mockResponse);
        });
    });

    describe('getUserSupports', () => {
        it('should return user support requests', async () => {
            const mockResponse = {
                supports: [mockSupport],
                total: 1,
                page: 1,
                totalPages: 1,
            };

            mockSupportService.getUserSupports.mockResolvedValue(mockResponse);

            const result = await controller.getUserSupports(mockRequest, '1', '10');

            expect(service.getUserSupports).toHaveBeenCalledWith(
                mockRequest.user.sub,
                1,
                10,
            );
            expect(result).toEqual(mockResponse);
        });
    });

    describe('getSupportById', () => {
        it('should return support request by id', async () => {
            const supportId = new Types.ObjectId().toString();

            mockSupportService.getSupportById.mockResolvedValue(mockSupport);

            const result = await controller.getSupportById(supportId);

            expect(service.getSupportById).toHaveBeenCalledWith(supportId);
            expect(result).toEqual(mockSupport);
        });
    });

    describe('updateSupport', () => {
        it('should update support request', async () => {
            const supportId = new Types.ObjectId().toString();
            const updateSupportDto: UpdateSupportDto = {
                title: 'Updated Title',
            };

            mockSupportService.updateSupport.mockResolvedValue(mockSupport);

            const result = await controller.updateSupport(
                supportId,
                updateSupportDto,
                mockRequest,
            );

            expect(service.updateSupport).toHaveBeenCalledWith(
                supportId,
                updateSupportDto,
                mockRequest.user.sub,
                undefined,
            );
            expect(result).toEqual(mockSupport);
        });
    });

    describe('deleteSupport', () => {
        it('should delete support request', async () => {
            const supportId = new Types.ObjectId().toString();
            const mockResponse = { message: 'Support request deleted successfully' };

            mockSupportService.deleteSupport.mockResolvedValue(mockResponse);

            const result = await controller.deleteSupport(supportId, mockRequest);

            expect(service.deleteSupport).toHaveBeenCalledWith(
                supportId,
                mockRequest.user.sub,
            );
            expect(result).toEqual(mockResponse);
        });
    });
});
