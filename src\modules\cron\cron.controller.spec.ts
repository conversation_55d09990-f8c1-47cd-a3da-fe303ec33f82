import { Test, TestingModule } from '@nestjs/testing';
import { CronController } from './cron.controller';
import { CronService } from './cron.service';
import { JwtAuthGuard } from '../../shared/guards/jwt-auth.guard';

describe('CronController', () => {
  let controller: CronController;
  let cronService: CronService;

  const mockCronService = {
    triggerBookingCompletionCheck: jest.fn(),
  };

  const mockJwtAuthGuard = {
    canActivate: jest.fn(() => true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CronController],
      providers: [
        {
          provide: CronService,
          useValue: mockCronService,
        },
      ],
    })
    .overrideGuard(JwtAuthGuard)
    .useValue(mockJwtAuthGuard)
    .compile();

    controller = module.get<CronController>(CronController);
    cronService = module.get<CronService>(CronService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('triggerBookingCompletion', () => {
    it('should trigger booking completion check and return success response', async () => {
      mockCronService.triggerBookingCompletionCheck.mockResolvedValue(undefined);

      const result = await controller.triggerBookingCompletion();

      expect(cronService.triggerBookingCompletionCheck).toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        message: 'Booking completion check triggered successfully'
      });
    });

    it('should handle errors from cron service', async () => {
      const error = new Error('Cron service error');
      mockCronService.triggerBookingCompletionCheck.mockRejectedValue(error);

      await expect(controller.triggerBookingCompletion()).rejects.toThrow('Cron service error');
    });
  });
});
