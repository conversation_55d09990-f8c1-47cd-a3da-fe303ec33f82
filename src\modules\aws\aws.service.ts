// src/aws/aws.service.ts
import { Injectable } from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import { randomUUID } from 'crypto';

@Injectable()
export class AwsService {
  private readonly s3Client: S3Client;
  private readonly bucketName = process.env.BUCKET_NAME;

  constructor() {
    this.s3Client = new S3Client({
      region: process.env.REGION, 
      credentials: {
        accessKeyId: process.env.ACCESS_KEY_ID!,
        secretAccessKey: process.env.secretKeyId!,
      },
    });
  }

  async uploadFile(file: Express.Multer.File): Promise<string> {
    const key = `${randomUUID()}-${file.originalname}`;

    await this.s3Client.send(
      new PutObjectCommand({
        Bucket: process.env.BUCKET_NAME,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
      }),
    );
   
 
    return `https://${process.env.BUCKET_NAME}.s3.${process.env.REGION}.amazonaws.com/${key}`;
  }

async uploadMultipleFiles(files: Express.Multer.File[]): Promise<string[]> {
  const urls: string[] = [];

  for (const file of files) {
    const key = `${randomUUID()}-${file.originalname}`;
    await this.s3Client.send(
      new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
      }),
    );
    urls.push(`https://${this.bucketName}.s3.${process.env.REGION}.amazonaws.com/${key}`);
  }

  return urls;
}

  async deleteFile(url: string): Promise<any> {
  try {
    const key = url.split(
      `https://${process.env.BUCKET_NAME}.s3.${process.env.REGION}.amazonaws.com/`
    )[1];
    const params = {
      Bucket: process.env.BUCKET_NAME,
      Key: key,
    };
    const command = new DeleteObjectCommand(params);
    const resp = await this.s3Client.send(command);
    return resp
  } catch (error) {
    throw error;
  }
}
  async extractS3Key(fileUrl:string){
    const url=new URL(fileUrl);
    
  }
}
