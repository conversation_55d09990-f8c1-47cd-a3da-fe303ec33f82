import { <PERSON><PERSON>tring, Is<PERSON><PERSON>ber, IsBoolean, IsMongoId, IsDate } from 'class-validator';
import { Type } from 'class-transformer';


export class PetLogDto {
  @IsMongoId()
  petId: string;

  @IsNumber()
  @Type(() => Number)
  health: number;

  @IsBoolean()
  @Type(() => Boolean)
  eating: boolean;

  @IsBoolean()
  @Type(() => Boolean)
  normalP: boolean;

  @IsBoolean()
  @Type(() => Boolean)
  sleepCycle: boolean;

  @IsBoolean()
  @Type(() => Boolean)
  movedE: boolean;

  @IsBoolean()
  @Type(() => Boolean)
  tookMed: boolean;

  @IsString()
  behavious: string

  @IsString()
  physicalSymptoms: string

  @IsDate()
  @Type(()=>Date)
  date: Date

}
