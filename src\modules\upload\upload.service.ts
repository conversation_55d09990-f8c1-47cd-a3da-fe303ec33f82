import { Injectable } from '@nestjs/common';
import { AwsService } from '../aws/aws.service';

@Injectable()
export class UploadService {
  constructor(private readonly awsService: AwsService) {}

  async uploadFiles(file: Express.Multer.File): Promise<string> {
    return await this.awsService.uploadFile(file); // <-- match method name
  }
  async uploadMultipleFiles(files: Express.Multer.File[]): Promise<string[]> {
    return await this.awsService.uploadMultipleFiles(files);
  }
  async deleteFile(url: string): Promise<any> {
    return await this.awsService.deleteFile(url);
  }
}
