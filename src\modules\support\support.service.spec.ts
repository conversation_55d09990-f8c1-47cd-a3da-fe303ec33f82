import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { SupportService } from './support.service';
import { Support, SupportDocument } from './schemas/support.schema';
import { UploadService } from '../upload/upload.service';
import { CreateSupportDto } from './dto/create-support.dto';
import { UpdateSupportDto } from './dto/update-support.dto';
import { NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';

describe('SupportService', () => {
    let service: SupportService;
    let model: Model<SupportDocument>;
    let uploadService: UploadService;

    const mockSupport = {
        _id: new Types.ObjectId(),
        userId: new Types.ObjectId(),
        title: 'Test Support',
        description: 'Test Description',
        image: 'test-image.jpg',
        createdAt: new Date(),
        updatedAt: new Date(),
        save: jest.fn(),
    };

    const mockSupportModel = {
        new: jest.fn().mockResolvedValue(mockSupport),
        constructor: jest.fn().mockResolvedValue(mockSupport),
        find: jest.fn(),
        findById: jest.fn(),
        findByIdAndUpdate: jest.fn(),
        findByIdAndDelete: jest.fn(),
        countDocuments: jest.fn(),
        create: jest.fn(),
        exec: jest.fn(),
    };

    const mockUploadService = {
        uploadFiles: jest.fn().mockResolvedValue('uploaded-image-url.jpg'),
    };

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                SupportService,
                {
                    provide: getModelToken(Support.name),
                    useValue: mockSupportModel,
                },
                {
                    provide: UploadService,
                    useValue: mockUploadService,
                },
            ],
        }).compile();

        service = module.get<SupportService>(SupportService);
        model = module.get<Model<SupportDocument>>(getModelToken(Support.name));
        uploadService = module.get<UploadService>(UploadService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createSupport', () => {
        it('should create a support request successfully', async () => {
            const createSupportDto: CreateSupportDto = {
                title: 'Test Support',
                description: 'Test Description',
            };
            const userId = new Types.ObjectId().toString();

            mockSupport.save.mockResolvedValue(mockSupport);
            jest.spyOn(model, 'constructor' as any).mockImplementation(() => mockSupport);

            const result = await service.createSupport(createSupportDto, userId);

            expect(result).toEqual(mockSupport);
            expect(mockSupport.save).toHaveBeenCalled();
        });

        it('should upload file and create support request', async () => {
            const createSupportDto: CreateSupportDto = {
                title: 'Test Support',
                description: 'Test Description',
            };
            const userId = new Types.ObjectId().toString();
            const file = {} as Express.Multer.File;

            mockSupport.save.mockResolvedValue(mockSupport);
            jest.spyOn(model, 'constructor' as any).mockImplementation(() => mockSupport);

            const result = await service.createSupport(createSupportDto, userId, file);

            expect(uploadService.uploadFiles).toHaveBeenCalledWith(file);
            expect(result).toEqual(mockSupport);
        });
    });

    describe('getAllSupports', () => {
        it('should return paginated support requests', async () => {
            const mockSupports = [mockSupport];
            const mockQuery = {
                populate: jest.fn().mockReturnThis(),
                sort: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                limit: jest.fn().mockReturnThis(),
                exec: jest.fn().mockResolvedValue(mockSupports),
            };

            mockSupportModel.find.mockReturnValue(mockQuery);
            mockSupportModel.countDocuments.mockResolvedValue(1);

            const result = await service.getAllSupports(1, 10);

            expect(result).toEqual({
                supports: mockSupports,
                total: 1,
                page: 1,
                totalPages: 1,
            });
        });
    });

    describe('getSupportById', () => {
        it('should return support request by id', async () => {
            const supportId = new Types.ObjectId().toString();
            const mockQuery = {
                populate: jest.fn().mockReturnThis(),
                exec: jest.fn().mockResolvedValue(mockSupport),
            };

            mockSupportModel.findById.mockReturnValue(mockQuery);

            const result = await service.getSupportById(supportId);

            expect(result).toEqual(mockSupport);
            expect(mockSupportModel.findById).toHaveBeenCalledWith(supportId);
        });

        it('should throw NotFoundException when support not found', async () => {
            const supportId = new Types.ObjectId().toString();
            const mockQuery = {
                populate: jest.fn().mockReturnThis(),
                exec: jest.fn().mockResolvedValue(null),
            };

            mockSupportModel.findById.mockReturnValue(mockQuery);

            await expect(service.getSupportById(supportId)).rejects.toThrow(NotFoundException);
        });

        it('should throw BadRequestException for invalid id format', async () => {
            const invalidId = 'invalid-id';

            await expect(service.getSupportById(invalidId)).rejects.toThrow(BadRequestException);
        });
    });

    describe('deleteSupport', () => {
        it('should delete support request successfully', async () => {
            const supportId = new Types.ObjectId().toString();
            const userId = new Types.ObjectId().toString();
            const mockSupportToDelete = { ...mockSupport, userId: new Types.ObjectId(userId) };

            mockSupportModel.findById.mockResolvedValue(mockSupportToDelete);
            mockSupportModel.findByIdAndDelete.mockResolvedValue(mockSupportToDelete);

            const result = await service.deleteSupport(supportId, userId);

            expect(result).toEqual({ message: 'Support request deleted successfully' });
            expect(mockSupportModel.findByIdAndDelete).toHaveBeenCalledWith(supportId);
        });

        it('should throw ForbiddenException when user tries to delete others support', async () => {
            const supportId = new Types.ObjectId().toString();
            const userId = new Types.ObjectId().toString();
            const differentUserId = new Types.ObjectId().toString();
            const mockSupportToDelete = { ...mockSupport, userId: new Types.ObjectId(differentUserId) };

            mockSupportModel.findById.mockResolvedValue(mockSupportToDelete);

            await expect(service.deleteSupport(supportId, userId)).rejects.toThrow(ForbiddenException);
        });
    });
});
