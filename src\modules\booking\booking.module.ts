import { Module } from '@nestjs/common';
import { BookingController } from './booking.controller';
import { BookingService } from './booking.service';
import { UsersModule } from '../users/users.module'; // Import UsersModule to get UsersService
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from '../users/schemas/user.schema';
import { booking, bookingSchema } from './schemas/bookingSchemas';
import { PaymentModule } from '../../payment/payment.module';
@Module({
  imports: [
    UsersModule,PaymentModule,
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: booking.name, schema: bookingSchema }, 
    ]),
  ],
  controllers: [BookingController],
  providers: [BookingService],
  exports: [BookingService],
})
export class BookingModule {}
