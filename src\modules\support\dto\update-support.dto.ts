import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "class-validator";

export class UpdateSupportDto {
    @IsOptional()
    @IsString()
    @MinLength(3, { message: 'Title must be at least 3 characters long' })
    @MaxLength(100, { message: 'Title must not exceed 100 characters' })
    readonly title?: string;

    @IsOptional()
    @IsString()
    @MinLength(10, { message: 'Description must be at least 10 characters long' })
    @MaxLength(1000, { message: 'Description must not exceed 1000 characters' })
    readonly description?: string;

    @IsOptional()
    @IsString()
    readonly image?: string;
}
