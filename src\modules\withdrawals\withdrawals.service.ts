import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Withdrawal, WithdrawalDocument } from './schemas/withdrawal.schema';

@Injectable()
export class WithdrawalsService {
  constructor(
    @InjectModel(Withdrawal.name) private withdrawalModel: Model<WithdrawalDocument>,
  ) {}

  /**
   * Create a new withdrawal record
   */
  async createWithdrawal(withdrawalData: {
    userId: string | Types.ObjectId;
    amount: number;
    user: string | Types.ObjectId;
    payout_id: string;
    payout_status: string;
    currency?: string;
  }): Promise<WithdrawalDocument> {
    const newWithdrawal = new this.withdrawalModel({
      userId: withdrawalData.userId,
      amount: withdrawalData.amount,
      user: withdrawalData.user,
      payout_id: withdrawalData.payout_id,
      payout_status: withdrawalData.payout_status,
      currency: withdrawalData.currency || 'usd',
    });

    return await newWithdrawal.save();
  }

  /**
   * Get all withdrawals for a specific user
   */
async getUserWithdrawals(userId: string): Promise<any> {
  // Validate ObjectId format first
  if (!Types.ObjectId.isValid(userId)) {
    throw new NotFoundException('Invalid user ID');
  }

  console.log("userId", userId);
  
  const userObjectId = new Types.ObjectId(userId);
  
  // Query for withdrawals using both possible field names
  const withdrawals = await this.withdrawalModel
    .find({ 
      $or: [
        { userId: userId },
        { user: userId }
      ]
    })
    .populate('userId', 'userName email')
    .populate('user', 'userName email') // Populate both fields
    .sort({ createdAt: -1 })
    .exec();

  // Check if no withdrawals found after the query
  if (!withdrawals || withdrawals.length === 0) {
    return{
      success: false,
      message: 'No withdrawals found for this user',
      data: []
    };
  }

  return withdrawals;
}

  /**
   * Get withdrawal by payout ID
   */
  async getWithdrawalByPayoutId(payoutId: string): Promise<WithdrawalDocument | null> {
    return await this.withdrawalModel
      .findOne({ payout_id: payoutId })
      .populate('userId', 'userName email')
      .exec();
  }

  /**
   * Update withdrawal status
   */
  async updateWithdrawalStatus(payoutId: string, status: string): Promise<WithdrawalDocument | null> {
    return await this.withdrawalModel
      .findOneAndUpdate(
        { payout_id: payoutId },
        { payout_status: status, updatedAt: new Date() },
        { new: true }
      )
      .populate('userId', 'userName email')
      .exec();
  }

  /**
   * Get all withdrawals (admin function)
   */
  async getAllWithdrawals(page: number = 1, limit: number = 10): Promise<{
    withdrawals: WithdrawalDocument[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;
    
    const [withdrawals, total] = await Promise.all([
      this.withdrawalModel
        .find()
        .populate('userId', 'userName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.withdrawalModel.countDocuments()
    ]);

    return {
      withdrawals,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Get withdrawal statistics for a user
   */
  async getUserWithdrawalStats(userId: string): Promise<{
    totalWithdrawals: number;
    totalAmount: number;
    successfulWithdrawals: number;
    pendingWithdrawals: number;
    failedWithdrawals: number;
  }> {
    if (!Types.ObjectId.isValid(userId)) {
      throw new NotFoundException('Invalid user ID');
    }

    const userObjectId = new Types.ObjectId(userId);
    
    const stats = await this.withdrawalModel.aggregate([
      { $match: { userId: userObjectId } },
      {
        $group: {
          _id: null,
          totalWithdrawals: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          successfulWithdrawals: {
            $sum: { $cond: [{ $eq: ['$payout_status', 'paid'] }, 1, 0] }
          },
          pendingWithdrawals: {
            $sum: { $cond: [{ $eq: ['$payout_status', 'pending'] }, 1, 0] }
          },
          failedWithdrawals: {
            $sum: { $cond: [{ $eq: ['$payout_status', 'failed'] }, 1, 0] }
          }
        }
      }
    ]);

    return stats[0] || {
      totalWithdrawals: 0,
      totalAmount: 0,
      successfulWithdrawals: 0,
      pendingWithdrawals: 0,
      failedWithdrawals: 0
    };
  }

async getYearlyWithdrawals(userId: string, year: string) {
  try {
    const userObjectId = new Types.ObjectId(userId);
    
    // Create date range for the specified year
    const startOfYear = new Date(`${year}-01-01T00:00:00.000Z`);
    const endOfYear = new Date(`${year}-12-31T23:59:59.999Z`);
    
    // Find withdrawals for the user within the specified year
    const withdrawals = await this.withdrawalModel.find({
      userId: userId,
      payout_status: 'paid',
      createdAt: {
        $gte: startOfYear,
        $lte: endOfYear
      }
    }).exec();
    
    if (!withdrawals || withdrawals.length === 0) {
      throw new NotFoundException(`No withdrawals found for user in year ${year}`);
    }
    
    // Calculate total amount and group by month (optional)
    const totalAmount = withdrawals.reduce((sum, withdrawal) => sum + withdrawal.amount, 0);
    
    // Group by month for better insights
    const monthlyBreakdown = withdrawals.reduce((acc, withdrawal) => {
      const month = withdrawal.createdAt.getMonth() + 1; // 1-12
      const monthName = new Date(0, month - 1).toLocaleString('en', { month: 'long' });
      
      if (!acc[monthName]) {
        acc[monthName] = {
          count: 0,
          totalAmount: 0,
          withdrawals: []
        };
      }
      
      acc[monthName].count += 1;
      acc[monthName].totalAmount += withdrawal.amount;
      acc[monthName].withdrawals.push(withdrawal);
      
      return acc;
    }, {});
    
    return {
      year: parseInt(year),
      totalWithdrawals: withdrawals.length,
      totalAmount,
      currency: withdrawals[0]?.currency || 'usd',
      monthlyBreakdown,
      withdrawals
    };
    
  } catch (error) {
    if (error instanceof NotFoundException) {
      throw error;
    }
    throw new BadRequestException(`Failed to retrieve yearly withdrawals: ${error.message}`);
  }
}
}
