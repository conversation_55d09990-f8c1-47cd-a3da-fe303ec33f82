import { <PERSON>, Post, Body,Get, Param, UseGuards, <PERSON>ers, Res, HttpStatus,Req,RawBodyRequest } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { JwtAuthGuard } from 'src/shared/guards/jwt-auth.guard';
import { JwtVeterniaGuard } from 'src/shared/guards/jwt-veternia';
import { Request, Response } from 'express';
import stripe from "stripe";
const Stripe = new stripe("sk_test_51Rfnde2XCV0AWrFEHGSgTu7BiVDkOSg5zb1rmQhlAm7ZbEr4ymG8xVCFhcK8rFlSL0SvvyRIZ5p79qnWzk6LqIlp00K78H3fdS", {
  // @ts-ignore
  apiVersion: '2025-08-27.basil',
});

@Controller('payments')
export class PaymentController {
    constructor(private readonly paymentService: PaymentService) {}

    @Post('create-connected-account')
    async createConnectedAccount(@Body('email') email: string) {
        return this.paymentService.createConnectedAccount(email);
    }

    @Post('payout')
    async payout(
        @Body() payoutData: { connected_acc_id: string, connected_external_acc_id: string, amount: number }
    ) {
        return this.paymentService.payoutTheAmount(
            payoutData.connected_acc_id,
            payoutData.connected_external_acc_id,
            payoutData.amount
        );
    }

    @Post('create-stripe-connect')
    async createStripeConnect(@Body('userId') userId: string) {
        return this.paymentService.createStripeConnect(userId);
    }

    @Post('create-payment-intent')
    async createPaymentIntent(@Body() dto: { userId: string, amount: number, currency: string, paymentMethodId: string }) {
        return this.paymentService.createPaymentIntent(dto);
    }

    @Post('confirm-payment-intent')
    async confirmPaymentIntent(@Body() dto: { paymentIntentId: string, paymentMethodId: string }) {
        return this.paymentService.confirmPaymentIntent(dto.paymentIntentId, dto.paymentMethodId);
    }

    @Get('get-balance')
    async getBalance(@Body('connected_acc_id') connected_acc_id: string) {
        return this.paymentService.getBalance(connected_acc_id);
    }

    @Get('get-payouts')
    async getPayouts(@Body('connected_acc_id') connected_acc_id: string) {
        return this.paymentService.getPayouts(connected_acc_id);
    }

    @Get('get-connected-account-info/:connected_acc_id')
    async getConnectedAccountInfo(@Param('connected_acc_id') connected_acc_id: string) {
        return this.paymentService.getConnectedAccountInfo(connected_acc_id);
    }

    @Post('set-account-capabilities-active')
    async setAccountCapabilitiesActive(@Body('connected_acc_id') connected_acc_id: string) {
        return this.paymentService.setAccountCapabilitiesActive(connected_acc_id);
    }

    @Post('add-test-funds')
    async addTestFundsToConnectedAccount(@Body('connected_acc_id') connected_acc_id: string, @Body('amount') amount: number) {
        return this.paymentService.addTestFundsToConnectedAccount(connected_acc_id, amount);
    }

    @Get('get-payment-info/:paymentIntentId')
    async getPaymentInfo(@Param('paymentIntentId') paymentIntentId: string) {
        return this.paymentService.getPaymentInfo(paymentIntentId);
    }

    @UseGuards(JwtVeterniaGuard)
    @Get('total-earnings')
    async getTotalEarnings(@Req() req: Request) {
        const userId = req.user['sub'];
        return this.paymentService.getTotalEarnings(userId);
    }

    @UseGuards(JwtVeterniaGuard)
    @Get('account-info')
    async getAccountInfo(@Req() req: Request) {
        const userId = req.user['sub'];
        return this.paymentService.getAccountInfo(userId);
    }

    @Post('webhook')
    async handleStripeWebhook(
        @Req() req: RawBodyRequest<Request>,
        @Res() res: Response,
    ) {
        const endpointSecret = process.env.STRIPE_CONNECT_WHSEC;
        let event: stripe.Event;

        const sig = req.headers['stripe-signature'];
        console.log("Webhook received, sig:", sig);

        // Validate that the stripe-signature header exists
        if (!sig) {
            console.error('Missing stripe-signature header.');
            return res.status(400).send('Missing stripe-signature header.');
        }

        try {
            // Construct and verify the event
            event = Stripe.webhooks.constructEvent(
                req.rawBody,
                sig,
                endpointSecret,
            );
            console.log('Webhook event constructed successfully:', event.type);

            // IMMEDIATELY respond to Stripe - this prevents timeouts
            res.status(200).json({ received: true });

            // Process the webhook asynchronously in the background
            // This won't block the response to Stripe
            setImmediate(async () => {
                try {
                    console.log('Processing webhook event:', event.type);
                    await this.paymentService.handleWebhook(event);
                    console.log('Webhook processed successfully:', event.type);
                } catch (error) {
                    console.error('Error handling webhook event:', error);
                    // Log the error but don't fail the webhook since we already responded
                }
            });

        } catch (error) {
            console.error('Error constructing event:', error.message);
            return res.status(400).send(`Webhook Error: ${error.message}`);
        }
    }
}