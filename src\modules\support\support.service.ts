import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Support, SupportDocument } from './schemas/support.schema';
import { CreateSupportDto } from './dto/create-support.dto';
import { UpdateSupportDto } from './dto/update-support.dto';
import { UploadService } from '../upload/upload.service';

@Injectable()
export class SupportService {
    constructor(
        @InjectModel(Support.name) private supportModel: Model<SupportDocument>,
        private readonly uploadService: UploadService,
    ) {}

    async createSupport(dto: CreateSupportDto, userId: string, file?: Express.Multer.File): Promise<SupportDocument> {
        try {
            let imageUrl = dto.image || '';

            // Upload image to AWS if file is provided
            if (file) {
                imageUrl = await this.uploadService.uploadFiles(file);
            }

            const support = new this.supportModel({
                ...dto,
                userId: new Types.ObjectId(userId),
                image: imageUrl,
            });

            return await support.save();
        } catch (error) {
            throw new BadRequestException('Failed to create support request: ' + error.message);
        }
    }

    async getAllSupports(page: number = 1, limit: number = 10): Promise<{ supports: SupportDocument[], total: number, page: number, totalPages: number }> {
        try {
            const skip = (page - 1) * limit;
            
            const [supports, total] = await Promise.all([
                this.supportModel
                    .find()
                    .populate('userId', 'userName email profilePicUrl')
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limit)
                    .exec(),
                this.supportModel.countDocuments()
            ]);

            const totalPages = Math.ceil(total / limit);

            return {
                supports,
                total,
                page,
                totalPages
            };
        } catch (error) {
            throw new BadRequestException('Failed to fetch support requests: ' + error.message);
        }
    }

    async getUserSupports(userId: string, page: number = 1, limit: number = 10): Promise<{ supports: SupportDocument[], total: number, page: number, totalPages: number }> {
        try {
            const skip = (page - 1) * limit;
            
            const [supports, total] = await Promise.all([
                this.supportModel
                    .find({ userId: new Types.ObjectId(userId) })
                    .populate('userId', 'userName email profilePicUrl')
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limit)
                    .exec(),
                this.supportModel.countDocuments({ userId: new Types.ObjectId(userId) })
            ]);

            const totalPages = Math.ceil(total / limit);

            return {
                supports,
                total,
                page,
                totalPages
            };
        } catch (error) {
            throw new BadRequestException('Failed to fetch user support requests: ' + error.message);
        }
    }

    async getSupportById(supportId: string): Promise<SupportDocument> {
        try {
            if (!Types.ObjectId.isValid(supportId)) {
                throw new BadRequestException('Invalid support ID format');
            }

            const support = await this.supportModel
                .findById(supportId)
                .populate('userId', 'userName email profilePicUrl')
                .exec();

            if (!support) {
                throw new NotFoundException('Support request not found');
            }

            return support;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException('Failed to fetch support request: ' + error.message);
        }
    }

    async updateSupport(supportId: string, dto: UpdateSupportDto, userId: string, file?: Express.Multer.File): Promise<SupportDocument> {
        try {
            if (!Types.ObjectId.isValid(supportId)) {
                throw new BadRequestException('Invalid support ID format');
            }

            const support = await this.supportModel.findById(supportId);
            
            if (!support) {
                throw new NotFoundException('Support request not found');
            }

            // Check if the user owns this support request
            if (support.userId.toString() !== userId) {
                throw new ForbiddenException('You can only update your own support requests');
            }

            let imageUrl = dto.image;

            // Upload new image to AWS if file is provided
            if (file) {
                imageUrl = await this.uploadService.uploadFiles(file);
            }

            const updatedSupport = await this.supportModel
                .findByIdAndUpdate(
                    supportId,
                    { ...dto, ...(imageUrl && { image: imageUrl }) },
                    { new: true }
                )
                .populate('userId', 'userName email profilePicUrl')
                .exec();

            return updatedSupport;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException('Failed to update support request: ' + error.message);
        }
    }

    async deleteSupport(supportId: string, userId: string): Promise<{ message: string }> {
        try {
            if (!Types.ObjectId.isValid(supportId)) {
                throw new BadRequestException('Invalid support ID format');
            }

            const support = await this.supportModel.findById(supportId);
            
            if (!support) {
                throw new NotFoundException('Support request not found');
            }

            // Check if the user owns this support request
            if (support.userId.toString() !== userId) {
                throw new ForbiddenException('You can only delete your own support requests');
            }

            await this.supportModel.findByIdAndDelete(supportId);

            return { message: 'Support request deleted successfully' };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException('Failed to delete support request: ' + error.message);
        }
    }
}
